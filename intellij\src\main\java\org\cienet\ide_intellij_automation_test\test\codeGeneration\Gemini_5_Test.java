package org.cienet.ide_intellij_automation_test.test.codeGeneration;

import org.cienet.ide_intellij_automation_test.util.TimeUtil;
import org.cienet.ide_intellij_automation_test.util.CommonUtil;
import org.cienet.ide_intellij_automation_test.util.TestExceptionHandler;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import com.intellij.remoterobot.fixtures.CommonContainerFixture;
import com.intellij.remoterobot.fixtures.EditorFixture;
import com.intellij.remoterobot.fixtures.JPopupMenuFixture;
import com.intellij.remoterobot.utils.Keyboard;

import java.io.IOException;
import java.time.Duration;
import java.awt.event.KeyEvent;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static com.intellij.remoterobot.search.locators.Locators.byXpath;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_5_Test extends BaseGenerationTest {

    private static final int GENERATION_WAIT_TIME_MS = 2000;
    private static final int CLEANUP_DELAY_MS = 5000;

    @Test
    void Gemini_5() throws IOException {
        // Setup
        openResourceProjectForTest("Code Completion/empty.py");
        
        String prompt = """
            # Write a function to add two numbers and return the result
            """;

        EditorFixture editor = CommonUtil.prepareEditor(remoteRobot);
        Keyboard keyboard = new Keyboard(remoteRobot);

        // Enter prompt
        CommonUtil.enterTextByLines(keyboard, prompt);

        int initialTextLength = editor.getText().length();

        // Trigger code generation
        triggerGeminiCodeGeneration(editor);

        // Wait for generation and refocus editor

        CommonUtil.waitAndRefocusEditor(keyboard, editor, 5000);

        // Verify code generation
        String generatedText = editor.getText();
        int generatedTextLength = generatedText.trim().length();

        assertTrue(
            generatedTextLength > initialTextLength,
            String.format("Code generation failed. Generated text:\n%s\n", generatedText)
        );        
        
        // Cleanup
        CommonUtil.cleanupEditor(remoteRobot, CLEANUP_DELAY_MS);
    }

    private void triggerGeminiCodeGeneration(EditorFixture editor) {
        editor.rightClick();
        
        JPopupMenuFixture contextMenu = remoteRobot.find(
                JPopupMenuFixture.class,
                byXpath("//div[@class='HeavyWeightWindow']"),
                Duration.ofSeconds(10));
        contextMenu.findText("Gemini").click();

        CommonContainerFixture generateCodeOption = remoteRobot.find(
                CommonContainerFixture.class,
                byXpath("//div[@text='Gemini']//div[@text='Generate Code']"),
                Duration.ofSeconds(10));        
                generateCodeOption.click();
    }
}
