#!/bin/bash

usage() {
    echo -e "Usage: $(basename "$0") -t <testCase1>[,<testCase2>] [-f <file>] [-p <port>] [-s <serverHost>] [-o <outputFilePrefix>] [-h]"
    echo -e "\t-t <testCaseNames> Comma-separated list of test cases. eg. -t Gemini_6,Gemini_17,Gemini_18"
    echo -e "\t-f <file> File with one test case name per line"
    echo -e "\t-s <serverHost> Hostname or IP. Default: localhost"
    echo -e "\t-p <port> Port. Default: 8080"
    echo -e "\t-o <outputFilePrefix> Default: test_resp"
    echo -e "\t-h Show help"
}

# Defaults
serverHost="localhost"
port="8080"
outputPrefix="test_resp"
testCaseList=()
report=()
passCount=0
failCount=0
errorCount=0

while getopts ":t:s:p:o:f:h" opt; do
    case $opt in
        t)
            IFS=',' read -ra parts <<< "$OPTARG"
            testCaseList+=("${parts[@]}")
            ;;
        f)
            if [ -f "$OPTARG" ]; then
                while IFS= read -r line; do
                    [ -n "$line" ] && testCaseList+=("$line")
                done < "$OPTARG"
            else
                echo "❌ Error: File '$OPTARG' not found."
                exit 1
            fi
            ;;
        s) serverHost="$OPTARG" ;;
        p) port="$OPTARG" ;;
        o) outputPrefix="$OPTARG" ;;
        h) usage; exit 0 ;;
        \?|*) usage; exit 1 ;;
    esac
done

if [ "${#testCaseList[@]}" -eq 0 ]; then
    echo "❌ Error: At least one test case must be provided via -t or -f."
    usage
    exit 1
fi

echo "🔧 Running ${#testCaseList[@]} test case(s)..."
echo

for testCaseName in "${testCaseList[@]}"; do
    outputFile="${outputPrefix}_${testCaseName}.json"
    echo "▶️  Running test: $testCaseName"

    if ! curl -s "http://${serverHost}:${port}/tests/run/${testCaseName}" -o "$outputFile"; then
        echo "❌ CURL failed for $testCaseName"
        report+=("⚠️ $testCaseName: CURL ERROR")
        ((errorCount++))
        continue
    fi

    if grep -q '"testResult"[[:space:]]*:[[:space:]]*"Successful"' "$outputFile"; then
        echo "✅ $testCaseName passed"
        report+=("✅️ $testCaseName: PASS")
        ((passCount++))
    else
        echo "❌ $testCaseName failed"
        report+=("❌ $testCaseName: FAIL")
        ((failCount++))
    fi
    echo
done

# Summary
echo "📋 Test Summary Report:"
for line in "${report[@]}"; do
    echo "$line"
done

total=${#testCaseList[@]}
echo
echo "Total: $total | Passed: $passCount | Failed: $failCount | Errors: $errorCount"

# Exit code: 0 = all pass, 1 = curl errors, 2 = test failures
if [ $errorCount -gt 0 ]; then
    exit 1
elif [ $failCount -gt 0 ]; then
    exit 2
else
    exit 0
fi
