package com.cienet.idetest.test.generation;

import com.cienet.idetest.util.CommonUtil;
import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

import static com.cienet.idetest.util.UiUtil.findElement;
import static com.cienet.idetest.vo.TestConstant.ACTION_MENU_GENERATE_CODE_LABEL;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
@ExtendWith(TestExceptionHandler.class)
public class Gemini_4_Test extends BaseGenerationTest {

    /**
     * Gemini-4 : Trigger Code Generation by clicking on yellow light bulb icon- [VSC] - Version4
     * Can use "Generate Code" button to trigger code generation after highlighting the piece of code/comment
     * and clicking yellow light bulb icon.
     *
     * @throws IOException
     */
    @Test
    public void test() throws IOException {
        // given
        UiUtil.openResourceProjectForTest(driver, wait, "Completion/Python/Empty.py");

        // when
        actions.sendKeys("def add(a, b : int) -> int:").perform();
        actions.keyDown(CommonUtil.getCtrlKey()).sendKeys("a").keyUp(CommonUtil.getCtrlKey()).perform();
        TimeUtil.doDelay(1000);
        WebElement bulbWidget = findElement(wait, By.xpath("//*[contains(@class, 'lightbulb')]"));
        bulbWidget.click();
        TimeUtil.doDelay(1000);
        WebElement actionWidget = driver.findElement(By.className("action-widget"));
        List<WebElement> childElements = actionWidget.findElements(By.xpath(".//*"));
        Optional<WebElement> generateCodeOption = childElements.stream()
                .filter(webElement -> webElement.getText().equals(ACTION_MENU_GENERATE_CODE_LABEL)).findFirst();
        if (generateCodeOption.isPresent()) {
            actions.moveToElement(generateCodeOption.get()).click().perform();
        } else {
            throw new IllegalStateException(String.format("There is no %s action !", ACTION_MENU_GENERATE_CODE_LABEL));
        }
        TimeUtil.doDelay(3000);
        actions.sendKeys(Keys.TAB).perform();
        TimeUtil.doDelay(1000);
        WebElement editArea = findElement(wait, By.xpath("//*[contains(@class,'editor-scrollable')]"));
        String finalCode = editArea.getText();
        List<WebElement> codeLines = driver.findElements(By.className("view-line"));

        // Then
        assertTrue(codeLines.size() >= 2, "Code lines should >=2. Code=" + finalCode);
    }

}
