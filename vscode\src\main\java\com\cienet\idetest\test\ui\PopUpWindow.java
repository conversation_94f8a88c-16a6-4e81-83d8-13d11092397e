package com.cienet.idetest.test.ui;

import com.cienet.idetest.test.completion.BaseCompletionTest;
import com.cienet.idetest.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.WebDriverWait;

import static com.cienet.idetest.vo.TestConstant.UITEST_GOOGLE_PROJECT_ID;
import static com.cienet.idetest.util.UiUtil.findElement;

@Slf4j
public class PopUpWindow {

    private WebDriver driver;

    private WebDriverWait wait;

    public PopUpWindow(WebDriver driver, WebDriverWait wait) {
        this.driver = driver;
        this.wait = wait;
    }

    public void selectGoogleCloudProject() {
        log.debug("selectGoogleCloudProject() enter...UITEST_GOOGLE_PROJECT_ID={}", UITEST_GOOGLE_PROJECT_ID);
        TimeUtil.doDelay(4000);
        findElement(wait,
                By.xpath("//*[contains(@aria-label, 'cloud  Select a Google Cloud project')]")).click();
        findElement(wait,
                By.cssSelector("div.monaco-list-row[aria-label*='" + UITEST_GOOGLE_PROJECT_ID
                        + "'][aria-label*='id: " + UITEST_GOOGLE_PROJECT_ID + "']")).click();
    }

    // public void selectGoogleCloudProjectForChat() {
    //     log.debug("selectGoogleCloudProjectForChat() enter...UITEST_GOOGLE_PROJECT_ID={}", UITEST_GOOGLE_PROJECT_ID);
    //     WebElement element = findElement(wait, By.xpath("//span[contains(@class, 'monaco-highlighted-label') and text()='" + UITEST_GOOGLE_PROJECT_ID + "']"));
    //     element.click();
    // }

    public void selectGoogleCloudProjectByKeys() {
        log.debug("selectGoogleCloudProjectByKeys() enter...UITEST_GOOGLE_PROJECT_ID={}", UITEST_GOOGLE_PROJECT_ID);
        TimeUtil.doDelay(2000);
        By xpath = By.xpath("//*[contains(@class,'quick-input-widget show-file-icons')] " +
                "//*[contains(@class,'quick-input-and-message')] //input");
        WebElement inputEle = findElement(wait, xpath);
        inputEle.sendKeys(UITEST_GOOGLE_PROJECT_ID);
        TimeUtil.doDelay(2000);
        inputEle.sendKeys(Keys.ENTER);
        TimeUtil.doDelay(2000);
    }

    public void openDocumentation() {
        findElement(wait, By.xpath("//*[@aria-label='View Documentation']")).click();
    }
}
