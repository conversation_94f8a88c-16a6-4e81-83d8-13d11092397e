package com.cienet.idetest.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;

@Slf4j
public class YDoToolUtil {

    private static void executeCmd(String cmd, String... args) {
        ArrayList<String> command = new ArrayList<String>();
        try {
            command.add("ydotool");
            command.add(cmd);
            command.addAll(Arrays.asList(args));
            ProcessBuilder builder = new ProcessBuilder(command);

            // Suppress output and error streams
//            builder.redirectOutput(ProcessBuilder.Redirect.DISCARD);
//            builder.redirectError(ProcessBuilder.Redirect.DISCARD);

            Process process = builder.start();
            int exitCode = process.waitFor();
            log.debug("executeCmd() done...command={}, exited with code {}", command, exitCode);
        } catch (IOException | InterruptedException e) {
            // ignore
            log.error("executeCmd() fail. command={}", command, e);
        }
        TimeUtil.doDelay(150);
    }

    public static void mouseMove(String... args) {
        executeCmd("mouseMove", args);
    }

    public static void keyPress(String... args) {
        executeCmd("key", args);
    }

    public static void type(String... args) {
        executeCmd("type", args);
    }

}
