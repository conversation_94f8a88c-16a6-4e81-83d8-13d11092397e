package com.cienet.idetest.test.completion;

import com.cienet.idetest.test.ui.EditorArea;
import com.cienet.idetest.test.ui.SuggestPreview;
import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;

import java.io.IOException;
import java.util.List;

import static com.cienet.idetest.util.UiUtil.findElement;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
@ExtendWith(TestExceptionHandler.class)
public class Gemini_28_Test extends BaseCompletionTest{

    @Test
    public void testAcceptWord() throws IOException {
        // Given
        String filePath = "Code Completion/Python/odd_number.py";
        String comment = "# write a function to find odd numbers from an array slice";
        String funcDeclare = "def find_odd_numbers(array):";

        // Trigger Suggest
        SuggestPreview suggestPreview = new SuggestPreview(driver, wait, actions);
        String promptText = suggestPreview.triggerSuggest(filePath, comment, funcDeclare);

        // Sometimes only 1 line is suggested. This will fail the test.
        // String suggestCode = suggestPreview.getSuggestCode();
        // assertTrue(StringUtils.isNoneEmpty(suggestCode), "No code is suggested!");

        WebElement acceptWord = suggestPreview.getAcceptWord();
        String currentLineSuggestText = suggestPreview.getCurrentLineSuggestText();
        log.debug("currentLineSuggestText: {}", currentLineSuggestText);
        assertTrue(StringUtils.isNoneEmpty(currentLineSuggestText), "No code is suggested!");

        // Accept the first word
        acceptWord.click();
        TimeUtil.doDelay(1000);
        String currentLineSuggestText1 = suggestPreview.getCurrentLineSuggestText();
        log.debug("currentLineSuggestText1: {}", currentLineSuggestText1);
        assertTrue(currentLineSuggestText.length() > currentLineSuggestText1.length(),
                "Accept word in code completion failed.");
        String previewCode = suggestPreview.getPreviewCode();
        log.debug("previewCode: {}", previewCode);
        assertTrue(currentLineSuggestText1.length() > 0, "Remaining suggesting code is empty!");
        TimeUtil.doDelay(1000);

        // Accept the second word
        acceptWord.click();
        TimeUtil.doDelay(1000);
        String currentLineSuggestText2 = suggestPreview.getCurrentLineSuggestText();
        log.debug("currentLineSuggestText2 after accepting word: {}", currentLineSuggestText2);
        assertTrue(currentLineSuggestText1.length() > currentLineSuggestText2.length(),
                "Accept word 2nd time in code completion failed.");
        TimeUtil.doDelay(3000);
    }
    
}