<configuration>
    <logger name="com.cienet.idetest" level="DEBUG" />

    <appender name="FILE" class="ch.qos.logback.core.FileAppender">
        <file>logs/spring-boot-app.log</file>
        <append>true</append>
        <immediateFlush>true</immediateFlush>
        <filter class="com.cienet.idetest.logging.DebugLogFilter" />
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

<!--    <appender name="PER_REQUEST_FILE" class="ch.qos.logback.classic.sift.SiftingAppender">-->
<!--        <discriminator class="ch.qos.logback.classic.sift.MDCBasedDiscriminator">-->
<!--            <key>requestTimestamp</key>-->
<!--            <defaultValue>unknown</defaultValue>-->
<!--        </discriminator>-->
<!--        <filter class="com.cienet.idetest.logging.DebugLogFilter" />-->
<!--        <sift>-->
<!--            <appender name="PER_REQUEST_FILE_%X{requestTimestamp}" class="ch.qos.logback.core.FileAppender">-->
<!--                <file>logs/request-%X{requestTimestamp}.log</file>-->
<!--                <append>true</append>-->
<!--                <encoder>-->
<!--                    <pattern>%d{yyyy-MM-dd HH:mm:ss} %-5level %logger{36} - %msg%n</pattern>-->
<!--                </encoder>-->
<!--            </appender>-->
<!--        </sift>-->
<!--    </appender>-->

    <root level="info">
        <appender-ref ref="FILE" />
<!--        <appender-ref ref="PER_REQUEST_FILE" />-->
    </root>
</configuration>