package com.cienet.idetest.test.ui;

import com.cienet.idetest.util.CommonUtil;
import com.cienet.idetest.util.UiUtil;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.*;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.support.ui.WebDriverWait;

import java.util.List;

import static com.cienet.idetest.util.UiUtil.findElement;

@Slf4j
public class EditorArea {

    private WebDriver driver;

    private WebDriverWait wait;

    private Actions actions;

    public EditorArea(WebDriver driver, WebDriverWait wait) {
        this.driver = driver;
        this.wait = wait;
        this.actions = new Actions(driver);
    }

    public EditorArea(WebDriver driver, WebDriverWait wait, Actions actions) {
        this.driver = driver;
        this.wait = wait;
        this.actions = actions;
    }

    public WebElement getEditorArea() {
        return findElement(wait, "//*[contains(@class,'editor-scrollable')]");
    }

    public List<WebElement> getEditorLines() {
        By xpathLine = By.xpath("//*[@id='workbench.parts.editor']" +
                "//*[contains(@class,'monaco-mouse-cursor-text')]//*[contains(@class,'view-line')]");
        List<WebElement> editorLines = UiUtil.findElements(wait, xpathLine);
        return editorLines;
    }

    // Not sure if it gets the same result as getEditorText()
    public String getTextInEditorLines() {
        StaleElementReferenceException e = null;

        for (int i = 0; i < 3; i++) {
            try {
                List<WebElement> editorLines = getEditorLines();
                StringBuilder sb = new StringBuilder();
                for (WebElement element : editorLines) {
                    sb.append(element.getText()).append("\n");
                }
                return sb.toString();
            } catch (StaleElementReferenceException se) {
                log.warn("Got StaleElementReferenceException! Will retry... i={}", i);
                e = se;
            }
        }

        throw e;
    }

    // Warning: It will get all the text in the editor including main text, hint and suggested code.
    public String getEditorText() {
        return getEditorArea().getText();
    }

    public void clearEditorText() {
        Keys ctrlKey = CommonUtil.getCtrlKey();
        actions.keyDown(ctrlKey).sendKeys("a").keyUp(ctrlKey).perform();
        actions.sendKeys(Keys.DELETE).perform();
    }
}
