```mermaid
flowchart TB
    %% USER INTERFACE LAYER
    subgraph UI["User Interface"]
        UI_1("Swagger UI<br/>CI/CD System<br/>CLI Tools")
    end

    %% APPLICATION LAYER
    subgraph APP["Test Automation Agent"]
        direction TB

        subgraph API["API Layer"]
            direction TB
            API_1("Spring Boot API")
            API_2("Test Run Controller")
        end

        subgraph SVC["Service Layer"]
            direction TB
            SVC_1("IntelliJ Manager")
            SVC_2("Test Runner")
            SVC_3("Error Handler")
            SVC_4("Debug Log Collector")
        end

        subgraph EXE["Execution Layer"]
            direction TB
            subgraph TST_CASE["Test Cases"]
               TST_CASE_1@{ shape: procs, label: "Test Case Implementation, e.g., Gemini_15_Test.java, Gemini_47_Test.java, Gemini_105_Test.java ..."}
            end
            subgraph TST_FRM["Test Frameworks"]
               direction TB
               TST_FRM_1("JUnit 5 Engine")
               TST_FRM_2("IntelliJ Remote Robot Core")
            end
        end
    end

    %% IDE LAYER
    subgraph IDE["IDE Environment"]
        IDE_1("IntelliJ IDEA<br/>Gemini Code Assist")
    end

    %% FLOW CONNECTIONS
    UI e1@<==>|HTTP Requests / Responses| APP
    API e2@-->|Delegate Operations| SVC
    SVC e3@-->|Execute Tests| EXE
    EXE e4@-->|Results & Logs| SVC
    SVC e5@-->|Test Summary| API
    APP e6@<==>|UI Commands & Results| IDE

    %% STYLING DEFINITIONS
    classDef uiStyle fill:#e5e7eb,stroke:#94a3b8,stroke-width:2px,color:#1f2937,font-weight:bold
    classDef appStyle fill:#f3f4f6,stroke:#cbd5e1,stroke-width:2px,color:#1e293b,font-weight:bold
    classDef apiStyle fill:#f9fafb,stroke:#60a5fa,stroke-width:2px,color:#1e3a8a,font-weight:bold
    classDef svcStyle fill:#f1f5f9,stroke:#38bdf8,stroke-width:2px,color:#0c4a6e,font-weight:bold
    classDef exeStyle fill:#fef3c7,stroke:#facc15,stroke-width:2px,color:#78350f,font-weight:bold
    classDef ideStyle fill:#ecfdf5,stroke:#34d399,stroke-width:2px,color:#064e3b,font-weight:bold

    classDef nodeStyle fill:#ffffff,stroke:#d1d5db,stroke-width:1.5px,color:#1f2937,font-weight:500

    class UI uiStyle
    class APP appStyle
    class API apiStyle
    class SVC apiStyle
    class EXE apiStyle
    class TST_CASE svcStyle
    class TST_FRM svcStyle
    class IDE uiStyle

    class UI_1,API_1,API_2,SVC_1,SVC_2,SVC_3,SVC_4,TST_FRM_1,TST_FRM_2,TST_CASE_1,IDE_1 nodeStyle

    e1@{ animate: true }
    e2@{ animate: true }
    e3@{ animate: true }
    e4@{ animate: true }
    e5@{ animate: true }
    e6@{ animate: true }
```
