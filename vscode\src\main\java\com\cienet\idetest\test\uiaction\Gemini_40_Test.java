package com.cienet.idetest.test.uiaction;

// import com.cienet.idetest.util.CommonUtil; // Removed
// import org.junit.jupiter.api.AfterEach; // Removed
// import org.junit.jupiter.api.BeforeEach; // Removed
import org.junit.jupiter.api.Test;
import org.openqa.selenium.By;
// import org.openqa.selenium.WebDriver; // Removed
import org.openqa.selenium.WebElement;
// import org.openqa.selenium.chrome.ChromeDriver; // Removed
// import org.openqa.selenium.chrome.ChromeOptions; // Removed
// import org.openqa.selenium.support.ui.WebDriverWait; // Removed

// import java.time.Duration; // Removed

// import static com.cienet.idetest.util.UiUtil.checkRestrictedModeAndAccept; // Removed
import static com.cienet.idetest.util.UiUtil.findElement;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.cienet.idetest.test.uiaction.BaseUiActionTest;

public class Gemini_40_Test extends BaseUiActionTest {

    // Fields driver, wait are inherited from BaseUiActionTest
    // setup() method is inherited from BaseUiActionTest

    @Test
    public void test() {
        // When
        WebElement welcomePageTitle = findElement(wait, By.xpath("//a[@class='label-name' and text()='Cloud Code - Welcome']"));

        // Then
        assertTrue(welcomePageTitle.isDisplayed());
    }

    // tearDown() method is inherited from BaseTest (via BaseUiActionTest)
    // and handles driver.quit()

}
