package com.cienet.idetest.util;

import org.openqa.selenium.WebDriver;

public class SessionUtil {

    private static final ThreadLocal<WebDriver> driverThreadLocal = new ThreadLocal<>();

    private static final ThreadLocal<String> resultOidThreadLocal = new ThreadLocal<>();

    private static final ThreadLocal<String> testNameThreadLocal = new ThreadLocal<>();

    public static WebDriver getDriver() {
        return driverThreadLocal.get();
    }

    public static void deleteAll() {
        deleteDriver();
        // deleteResultOid(); resultOid is needed for screenShot
        deleteTestName();
    }

    public static void setDriver(WebDriver driver) {
        driverThreadLocal.set(driver);
    }

    public static void deleteDriver() {
        driverThreadLocal.remove();
    }

    public static String getResultOid() {
        return resultOidThreadLocal.get();
    }

    public static void setResultOid(String resultOid) {
        resultOidThreadLocal.set(resultOid);
    }

    public static void deleteResultOid() {
        resultOidThreadLocal.remove();
    }

    public static String getTestName() {
        return testNameThreadLocal.get();
    }

    public static void setTestName(String testName) {
        testNameThreadLocal.set(testName);
    }

    public static void deleteTestName() {
        testNameThreadLocal.remove();
    }

}
