package com.cienet.idetest.test.generation;

import com.cienet.idetest.test.BaseTest;
import com.cienet.idetest.test.ui.EditorArea;
import com.cienet.idetest.test.ui.RightClickContextMenu;
import com.cienet.idetest.test.ui.SuggestPreview;
import com.cienet.idetest.util.CommonUtil;
import com.cienet.idetest.util.RobotUtil;
import com.cienet.idetest.util.TimeUtil;
import com.cienet.idetest.util.UiUtil;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;

import java.io.IOException;
import java.util.List;

import static com.cienet.idetest.util.UiUtil.findElement;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
public class BaseGenerationTest extends BaseTest {

    protected void testCodeGenByText(String text) throws IOException {
        testCodeGenByText("Chat/empty.py", text);
    }

    protected void testCodeGenByText(String filePath, String text) throws IOException {
        testCodeGenByText(filePath, text, false);
    }

    protected void testCodeGenByText(String filePath, String text, boolean useHotKey) throws IOException {
        log.debug("filePath: '{}', text: '{}'", filePath, text);
        EditorArea editorArea = new EditorArea(driver, wait);

        // Given
        UiUtil.openResourceProjectForTest(driver, wait, filePath);
        clearEditorText();
        TimeUtil.doDelay(2000);

        // When
        actions.sendKeys(text).perform();
        actions.sendKeys(Keys.ENTER).perform();
        String editorTextBefore = editorArea.getTextInEditorLines().trim();
        log.debug("editorTextBefore: {}", editorTextBefore);
        TimeUtil.doDelay(3000);
        
        // Click ESC 3 times to dismiss Code Completion which might interfere Code Generation
        actions.sendKeys(Keys.ESCAPE).perform();
        TimeUtil.doDelay(500);
        actions.sendKeys(Keys.ESCAPE).perform();
        TimeUtil.doDelay(500);
        actions.sendKeys(Keys.ESCAPE).perform();
        TimeUtil.doDelay(500);

        if (useHotKey) {
            actions.keyDown(Keys.CONTROL).sendKeys(Keys.ENTER).keyUp(Keys.CONTROL).perform();
        } else {
            RightClickContextMenu rightClickContextMenu = new RightClickContextMenu(driver, wait);
            WebElement presentation = findElement(wait, By.cssSelector("div[role='presentation']"));
            rightClickContextMenu.triggerCodeGenFromContextMenu(wait, presentation, false);
        }

        TimeUtil.doDelay(3000);
        SuggestPreview suggestPreview = new SuggestPreview(driver, wait);
        WebElement previewElem = suggestPreview.getCurrentLineElement();
        assertNotNull(previewElem, "No code is suggested!");

        actions.sendKeys(Keys.TAB).perform();
        TimeUtil.doDelay(1000);
        actions.sendKeys(Keys.TAB).perform();
        TimeUtil.doDelay(3000);
        String editorTextAfter = editorArea.getTextInEditorLines().trim();
        log.debug("editorTextAfter: {}", editorTextAfter);
        assertTrue(editorTextAfter.length() > editorTextBefore.length(), "Generated code isn't inserted to editor.");
    }
}
