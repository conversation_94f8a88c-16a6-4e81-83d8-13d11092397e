package com.cienet.idetest.test.chat;

import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.test.ui.ChatWindow;
import com.cienet.idetest.test.ui.RightClickContextMenu;
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;

import java.io.IOException;
import java.util.List;

import static com.cienet.idetest.util.UiUtil.findElement;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
@ExtendWith(TestExceptionHandler.class)
public class Gemini_175_Test extends BaseChatTest {

    /**
     * Gemini-175 : Explain This without selection after switching between multiple files (same language) - Version2
     * "Explain This" code without a highlighted selection between different files of the same language
     *
     * Reviewed with test team on 17/10/2024
     * Test compared with TesLink on: 17/10/2024
     * @throws IOException
     */
    @Test
    public void test() throws IOException {
        // Given
        // the active window should be the last opened file
        UiUtil.openResourceProjectForTest(driver, wait, "Chat/multi-file/web_crawler_snippets.py",
                "Chat/multi-file/inheritance.py");

        // When
        TimeUtil.doDelayInSeconds(2);
        WebElement presentation = findElement(wait, By.cssSelector("div[role='presentation']"));
        RightClickContextMenu rightClickContextMenu = new RightClickContextMenu(driver, wait);
        ChatWindow chatWindow = rightClickContextMenu.triggerCodeExplain(presentation);
        WebElement chatResponse = chatWindow.waitUntilChatFinish();

        // Then
        String chatText1 = chatResponse.getText();
        assertTrue(chatText1.contains("FileMetadata"));
        assertFalse(chatText1.contains("can't"));

        chatWindow.stepOut();
        switchFile("//a[@class='action-label codicon codicon-explorer-view-icon']",
                "//div[@aria-label='web_crawler_snippets.py']");
        rightClickContextMenu.triggerCodeExplain(presentation);

        wait.until(driver -> {
            List<WebElement> elements = driver.findElements(By.cssSelector(".chat-history-item.system"));
            if (elements.size() != 2) {
                return false;
            }
            String chatText2 = elements.get(1).getText(); // 2nd chat response
            assertTrue(chatText2.contains("crawl_page"));
            assertFalse(chatText2.contains("can't"));
            return true;
        });
    }

    private void switchFile(String explorerFileXpath, String fileXpath) {
        findElement(wait, By.xpath(explorerFileXpath)).click();
        TimeUtil.doDelayInSeconds(2);

        List<WebElement> fileElements = driver.findElements(By.xpath(fileXpath));
        if (!fileElements.isEmpty()) {
            fileElements.get(0).click();
            TimeUtil.doDelayInSeconds(3);
        } else {
            log.info("File not found: " + fileXpath);
        }
    }

}
