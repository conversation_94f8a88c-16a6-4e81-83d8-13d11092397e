package com.cienet.idetest.test.completion;

import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.ide.test.common.vo.LanguageTypeEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.IOException;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_99_Test extends BaseCompletionTest {

    /**
     * Gemini-99
     * Basic sanity test that code completion can be triggered with Typescript.
     * Only tests if code completion can be triggered and returns a suggestion -
     * does not test the syntax, formatting, or quality of the suggestion.
     *
     * @throws IOException
     */
    @Test
    public void testTsCodeComplete() throws IOException {
        String filePath = "Code Completion/TypeScript/sortArray.ts";
        String comment = "// Given an array of integers nums, sort the array in ascending order using quick sort and return it";
        String funcDeclare = "function sortArray(nums: number[]): number[] {";
        doCompletionTest(filePath, comment, funcDeclare);
    }

}