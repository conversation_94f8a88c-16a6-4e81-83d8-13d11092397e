package com.cienet.idetest.test.completion;

import com.cienet.idetest.test.ui.SuggestPreview;
import com.cienet.idetest.util.TestExceptionHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
@ExtendWith(TestExceptionHandler.class)
public class Gemini_58_Test extends BaseCompletionTest {

    @Test
    public void testCodeCompletion() throws IOException {
        // Given
        String filePath = "Code Completion/Python/empty.py";
        String comment = "# write a function to add two numbers";
        String funcDeclare = "def add(a, b : int) -> int:";

        // When
        // Trigger Suggest
        SuggestPreview suggestPreview = new SuggestPreview(driver, wait, actions);
        String promptText = suggestPreview.triggerSuggest(filePath, comment, funcDeclare);
        log.debug("promptText: {}", promptText);
        String suggestCode = suggestPreview.getSuggestCode();
        log.debug("suggestCode: {}", suggestCode);

        // Then
        assertTrue(StringUtils.isNoneEmpty(suggestCode), "No code is suggested!");
    }

}
