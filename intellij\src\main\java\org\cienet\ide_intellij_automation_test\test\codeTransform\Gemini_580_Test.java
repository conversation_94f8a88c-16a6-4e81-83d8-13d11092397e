package org.cienet.ide_intellij_automation_test.test.codeTransform;

import org.cienet.ide_intellij_automation_test.util.TestExceptionHandler;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import com.cienet.ide.test.common.constants.FileContentIde;
import com.cienet.ide.test.common.constants.FileNameTest;
import com.cienet.ide.test.common.constants.PromptTransform;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_580_Test extends BaseTransformTest {

    @Test
    public void Gemini_580() {
        doSimpleTransformTest(FileNameTest.EMPTY_PYTHON, FileContentIde.DEFINE_KERAS_MODEL,
                PromptTransform.FIX_SYNTAX_CODE);
    }
}