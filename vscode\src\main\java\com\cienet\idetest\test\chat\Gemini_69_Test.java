package com.cienet.idetest.test.chat;

import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.test.ui.ChatWindow;
import com.cienet.idetest.test.ui.LeftManuBar;
import com.cienet.idetest.util.TestExceptionHandler;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.WebElement;

import java.io.IOException;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
@ExtendWith(TestExceptionHandler.class)
public class Gemini_69_Test extends BaseChatTest {

    /**
     * Gemini-69 : Validate Chat Window and Response from Conversation - Version4
     * Verifies the chat window appears and responds
     *
     * Test compared with TesLink on: 3/10/2024
     * Reviewed with test team on 16/10/2024
     */
    @Test
    public void test() throws IOException {
        // Given
        UiUtil.openResourceProjectForTest(driver, wait, "Chat/empty.py");

        // When
        LeftManuBar leftManuBar = new LeftManuBar(driver, wait);
        ChatWindow chatWindow = leftManuBar.openGeminiChat();
        chatWindow.stepInto();
        chatWindow.checkIfProjectIsElectedAndSelectIfNot();
        chatWindow.closeTipsIfVisible();

        WebElement chatInputField = chatWindow.getChatInputField();
        chatInputField.sendKeys("Give me a function that calculates a simple moving average for any period");
        chatWindow.getSubmitButton().click();

        // Then
        List<WebElement> chatHistory = chatWindow.waitUntilChatFinishAndGetHistory();
        assertTrue(chatHistory.size() >= 2, "Chat history div does not exist");
    }

}
