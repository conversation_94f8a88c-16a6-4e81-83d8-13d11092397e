```mermaid
flowchart RL
  subgraph user [User Interaction]
        direction LR
        UserInterface(Swagger UI,
        CI/CD System,
        or CLI)
  end
  subgraph spring_boot [Test Automation Agent]
    direction BT
    subgraph api_layer [API Layer]
        direction LR
        SpringBootAPI(SpringBoot API) --> |Process User Requests| TestRunController(TestRunController)
    end
    subgraph svc_layer [Service Layer]
        direction TB
        IntellijManagerService(Intellij Manager Service)
        TestRunnerService(Test Runner Service)
    end
    subgraph exc_layer [Execution & IDE Interaction]
        direction LR
        JUnit5Engine(JUnit 5 Engine) --> |Runs| Tests(Actual Test Classes, e.g., Gemini_15_Test)
        Tests --> |Uses| IntelliJRemoteRobotCore(IntelliJ Remote Robot Core)
        Tests --> |Uses| RH_UTIL(Red Hat Common UI Test Utils)
    end
  end
  subgraph ide [IDE Instance]
        direction LR
        IDE(IntelliJ IDEA
        with Gemini Code Assist)
    end

  user ==> |HTTP Request| spring_boot ==> |HTTP Response| user

  api_layer --> |Delegates IDE Control & Test Execution| svc_layer --> |Executes Test Cases| exc_layer
  exc_layer --> |Collects Results, Logs, and Screenshots| svc_layer --> |Summarize Test Results| api_layer

  spring_boot ==> |UI Commands| ide ==> |Test Result| spring_boot

```
