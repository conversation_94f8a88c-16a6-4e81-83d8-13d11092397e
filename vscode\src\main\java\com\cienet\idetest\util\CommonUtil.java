package com.cienet.idetest.util;

import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.Keys;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.channels.FileChannel;

@Slf4j
public class CommonUtil {

    public enum OsTypeEnum {Mac, Windows, Linux, Unknown};

    public static String getCurrentDir() {
        return System.getProperty("user.dir");
    }

    public static void validateIsMac() {
        if (!System.getProperty("os.name").toLowerCase().contains("mac")) {
            throw new UnsupportedOperationException("This test only runs on macOS.");
        }
    }

    public static boolean isMacOsAbove14() {
        if (getOsTyppe() != OsTypeEnum.Mac) {
            throw new RuntimeException("It's not MacOS");
        }
        String osVersion = System.getProperty("os.version");
        log.debug("osVersion: {}", osVersion);

        String[] versionParts = osVersion.split("\\.");
        if (versionParts.length > 0) {
            try {
                int majorVersion = Integer.parseInt(versionParts[0]);
                return majorVersion >= 14;
            } catch (NumberFormatException e) {
                throw new RuntimeException("Unable to parse macOS version: " + osVersion, e);
            }
        }
        return false;
    }

    public static OsTypeEnum getOsTyppe() {
        String osName = System.getProperty("os.name").toLowerCase();
        log.trace("osName: {}", osName);
        if (osName.contains("mac")) {
            return OsTypeEnum.Mac;
        } else if (osName.contains("win")) {
            return OsTypeEnum.Windows;
        } else if (osName.contains("nix") || osName.contains("nux") || osName.contains("aix")) {
            return OsTypeEnum.Linux;
        } else {
            return OsTypeEnum.Unknown;
        }
    }

    public static Keys getCtrlKey() {
        Keys ctrlKey = isMacOs() ? Keys.COMMAND : Keys.CONTROL;
        return ctrlKey;
    }

    public static boolean isMacOs() {
        return getOsTyppe() == OsTypeEnum.Mac;
    }

    public static boolean isWindows() {
        return getOsTyppe() == OsTypeEnum.Windows;
    }

    public static boolean isLinux() {
        return getOsTyppe() == OsTypeEnum.Linux;
    }

    public static void copyFileWithFlush(File srcFile, File destFile) throws IOException {
        try (FileInputStream fis = new FileInputStream(srcFile);
             FileOutputStream fos = new FileOutputStream(destFile);
             FileChannel srcChannel = fis.getChannel();
             FileChannel destChannel = fos.getChannel()) {
            destChannel.transferFrom(srcChannel, 0, srcChannel.size());
            destChannel.force(true); // Ensures data is flushed to disk
        }
    }
}
