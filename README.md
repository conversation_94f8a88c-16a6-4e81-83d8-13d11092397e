> [!NOTE]
> This is the internal repository intended for development purposes only.

# IDE Automation Test Suite

This project contains automated UI tests for two mainstream IDEs (Integrated Development Environment): VS Code (Visual Studio Code) and IntelliJ IDEA. The tests primarily focus on evaluating the functionality of the "Gemini Code Assist" feature within these IDEs.

## Project Structure

The project is organized into two main directories, each dedicated to testing a specific IDE:

- **vscode**: Contains the automation tests for Visual Studio Code.
- **intellij**: Contains the automation tests for IntelliJ IDEA.

### vscode

This directory houses the Selenium-based UI tests for VS Code. The tests are designed to interact with the VS Code interface, specifically targeting the "Gemini Code Assist" feature. Key components include:

- **Build System**: Gradle is used as the build tool.
- **Dependencies**: Includes Selenium for browser automation, JUnit for testing, and Spring Boot for creating a REST API to trigger tests.
- **REST API**: A Spring Boot application provides a REST API to initiate test runs. Swagger documentation is available at `/swagger-ui/index.html` when the application is running.
- **Configuration**: Tests can be configured using system properties or by modifying the `application.properties` file.
- **Test Execution**: Tests can be run individually from within IntelliJ IDEA or all together via the Gradle command line.
- **Screen Recording**: Optional screen recording during test execution can be enabled.

### intellij

This directory contains the automation tests for IntelliJ IDEA, utilizing the IntelliJ Remote Robot framework. The tests aim to assess various IDE behaviors, including code completion, code generation, and other AI-assisted development features provided by "Gemini Code Assist". Key components include:

- **Build System**: Gradle is used as the build tool.
- **Dependencies**: Includes the IntelliJ Remote Robot framework, JUnit for testing, and Spring Boot for creating a REST API to trigger tests.
- **REST API**: A Spring Boot application provides a REST API to manage and execute tests. Swagger UI is available at the root URL when the application is running.
- **Test Execution**: Tests can be triggered via REST API endpoints, allowing for remote control and automation of test runs.
- **Automation Capabilities**: Successfully automates features like code completion, code generation, and output log gathering.
- **Limitations**: Includes limitations such as project access restrictions, plugin stability issues, and the need for some manual setup steps.

## Common Features

- **REST API**: Both the `vscode` and `intellij` directories include a Spring Boot application that exposes a REST API. This API allows users to start tests, set up the environment, and retrieve test results programmatically.
- **Swagger Documentation**: Both projects provide `Swagger UI` for exploring and interacting with the REST API endpoints.
- **Test Framework**: Both projects use `JUnit` for defining and running tests, ensuring a standardized approach to test execution and reporting.

## Running the Tests

### VS Code

1. Open the project in IntelliJ IDEA.
2. Update the path to the `canonical-answer` repository in the `open_project.scpt` file.
3. Select and run the desired test within IntelliJ IDEA.
4. Alternatively, run all tests using the command: `./gradlew test`
5. Refer to [vscode/README.md](./vscode/README.md) for further instructions.

### IntelliJ IDEA

1. Clone the repository and navigate to the `intellij` directory.
2. Start the application using: `./gradlew bootRun`
3. Access the Swagger UI at the provided URL (typically `http://localhost:8080/`).
4. Use the API endpoints to start IntelliJ IDE, set up the Gemini Code Assist plugin, and run specific test cases.
5. Refer to [intellij/README.md](./intellij/README.md) for further instructions.

## Customization

Both projects allow for customization through configuration files or system properties, enabling users to adapt the tests to their specific environments and requirements.
