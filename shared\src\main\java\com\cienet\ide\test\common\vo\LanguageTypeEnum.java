package com.cienet.ide.test.common.vo;

public enum LanguageTypeEnum {
    C (0),
    Cplusplus (1),
    Csharp (2),
    <PERSON><PERSON><PERSON> (3),
    Typescript (4),
    Go (5),
    Javascript (6),
    Java (7),
    Python (8)
    ;

    private static LanguageTypeEnum[] allEnums = {
            C,
            Cplusplus,
            Csharp,
            Kotlin,
            Typescript,
            Go,
            Javascript,
            Java,
            Python
    };

    private LanguageTypeEnum(int value) {
    }

    public static LanguageTypeEnum[] getAllEnums() {
        return allEnums;
    }

    public int value() {
        return ordinal();
    }
}
