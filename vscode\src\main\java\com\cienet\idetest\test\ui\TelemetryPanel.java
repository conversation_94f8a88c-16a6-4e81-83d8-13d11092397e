package com.cienet.idetest.test.ui;

import com.cienet.idetest.util.CommonUtil;
import com.cienet.idetest.util.RobotUtil;
import com.cienet.idetest.util.TimeUtil;
import com.cienet.idetest.util.UiUtil;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.support.ui.Select;
import org.openqa.selenium.support.ui.WebDriverWait;

import java.awt.event.KeyEvent;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
public class TelemetryPanel {

    public static final String XPATH_OUTPUT = "//*[contains(@class,'action-item')] " +
            "//*[contains(text(), 'Output')]";

    private final WebDriver driver;

    private final WebDriverWait wait;

    private Actions actions;

    private String telemetryText;

    private static final Pattern SLASH_CMD_PATTERN = Pattern.compile("\"key\"\\s*:\\s*\"slash_command\"\\s*,\\s*\"value\"\\s*:\\s*\"([^\"]+)\"");

    private static final Pattern PLUGIN_IDE_PATTERN = Pattern.compile("\"key\"\\s*:\\s*\"editor_version\"\\s*,\\s*\"value\"\\s*:\\s*\"([^\"]+)\"");

    private static final Pattern PLUGIN_VER_PATTERN = Pattern.compile("\"key\"\\s*:\\s*\"ext_version\"\\s*,\\s*\"value\"\\s*:\\s*\"([^\"]+)\"");

    public TelemetryPanel(WebDriver driver, WebDriverWait wait, Actions actions) {
        this.driver = driver;
        this.wait = wait;
        this.actions = actions;
    }

    public void open() {
        TimeUtil.doDelay(1000);
        // ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView(true);", editorPane);
        // editorPane.click();
        openOutputPanel();
        TimeUtil.doDelay(2000);
        // Select Gemini Code Assist Telemetry in output
        WebElement dropdown = UiUtil.findElement(wait, "//div[@class='title-actions']" +
                "//div[@class='monaco-toolbar'] //*[contains(@class, 'select-container')] //select");
        Select select = new Select(dropdown);
        select.selectByVisibleText("Gemini Code Assist Telemetry");
    }

    public void close() {
        closeOutputPanel();
    }

    private String getTelemetryText() {
        if (telemetryText != null) {
            return telemetryText;
        }
        WebElement element = UiUtil.findElement(wait, "//div[contains(@class, 'output-view')]"
                + "//div[contains(@class, 'view-lines monaco-mouse-cursor-text')]");
        telemetryText = element.getText();
        return telemetryText;
    }

    public String getIdeVersion() {
        String teleText = getTelemetryText();
        log.debug("getIdeVersion() - teleText: {}", teleText);
        Matcher matcher = PLUGIN_IDE_PATTERN.matcher(teleText);
        String ideVersion = null;

        if (matcher.find()) {
            ideVersion = matcher.group(1);
            ideVersion = ideVersion.replaceAll("\\r?\\n|\\r", "");
            log.debug("ideVersion value: {}", ideVersion);
        } else {
            log.error("ideVersion Value not found.");
        }

        return ideVersion;
    }

    public String getPluginVersion() {
        String teleText = getTelemetryText();
        log.debug("getPluginVersion() - teleText: {}", teleText);
        Matcher matcher = PLUGIN_VER_PATTERN.matcher(teleText);
        String pluginVersion = null;

        if (matcher.find()) {
            pluginVersion = matcher.group(1);
            pluginVersion = pluginVersion.replaceAll("\\r?\\n|\\r", "");
            log.debug("ext_version value: {}", pluginVersion);
        } else {
            log.error("ext_version not found.");
        }

        return pluginVersion;
    }

    public void filterText(String text) {
        log.debug("filter {} in telemetry.", text);
        WebElement input = UiUtil.findElement(wait, "//div[contains(@class, 'ibwrapper')]//input[@placeholder='Filter']");
        input.sendKeys(text);
        TimeUtil.doDelay(1000);
    }

    public String getSlashCommand() {
        // Hide left panel
        Keys ctrlKey = CommonUtil.getCtrlKey();
        actions.keyDown(ctrlKey).sendKeys("b") .keyUp(ctrlKey).perform();

        TimeUtil.doDelay(1000);
        String teleText = getTelemetryText();
        TimeUtil.doDelay(1000);

        // Show left panel
        actions.keyDown(ctrlKey).sendKeys("b") .keyUp(ctrlKey).perform();

        log.debug("getSlashCommand() - teleText: {}", teleText);
        Matcher matcher = SLASH_CMD_PATTERN.matcher(teleText);
        String slashCmd = null;

        if (matcher.find()) {
            slashCmd = matcher.group(1);
            slashCmd = slashCmd.replaceAll("\\r?\\n|\\r", "");
            log.debug("slash_command value: {}", slashCmd);
        } else {
            log.error("slash_command not found.");
        }

        return slashCmd;
    }

    public void maximize() {
        log.debug("maximize telemetry panel.");
        // Clear notifications
        UiUtil.clearNotificatons(driver);
        WebElement maximizePanelBtn = UiUtil.findElement(wait, "//a[@aria-label='Maximize Panel Size']");
        TimeUtil.doDelay(3000);
        maximizePanelBtn.click();
        TimeUtil.doDelay(3000);
    }

    private void openOutputPanel() {
        if (isOutputPanelOpened()) {
            log.debug("Output panel already opened!");
            return;
        }

        toggleOutputPanel();
        WebElement outpuPanel = UiUtil.findElement(wait, XPATH_OUTPUT);
        assertTrue(outpuPanel.isDisplayed());
    }

    private void closeOutputPanel() {
        if (!isOutputPanelOpened()) {
            log.debug("Output panel already closed!");
            return;
        }

        toggleOutputPanel();

        for (int i = 0; isOutputPanelOpened() && i < 3; i++) {
            log.debug("Try to close Telemetry Panel retry: {}", i);
            toggleOutputPanel();
            TimeUtil.doDelay(5000);
        }

        if (isOutputPanelOpened()) {
            throw new RuntimeException("Failed to close Telemetry Panel!");
        }
    }

    private boolean isOutputPanelOpened() {
        if (UiUtil.isElementPresent(driver, XPATH_OUTPUT)) {
            return true;
        }

        return false;
    }

    private void toggleOutputPanel() {
        log.debug("Toggle output panel.");
        Keys ctrlKey = CommonUtil.getCtrlKey();

        switch (CommonUtil.getOsTyppe()) {
        case Mac, Windows ->
            // Shit+Ctrl+U to open output panel.
            actions.keyDown(Keys.SHIFT).keyDown(ctrlKey).sendKeys("u")
                        .keyUp(ctrlKey).keyUp(Keys.SHIFT).perform();
        case Linux -> {
                // Ctrl+K Ctrl+H to open output panel.
                // actions doesn't work
                RobotUtil.clickCtrlAndKey(KeyEvent.VK_K);
                RobotUtil.clickCtrlAndKey(KeyEvent.VK_H);
            }
        }
    }
}
