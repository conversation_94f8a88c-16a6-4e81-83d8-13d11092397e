package com.cienet.idetest.test.chat;

import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.test.ui.ChatWindow;
import com.cienet.idetest.test.ui.LeftManuBar;
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;

import java.io.IOException;
import java.util.List;

import static com.cienet.idetest.util.UiUtil.findElement;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_76_Test extends BaseChatTest {

    /**
     * Gemini-76 : Insert code into file from chat - Version4
     * Can insert code from chat into file
     *
     * Test compared with TesLink on: 3/10/2024
     * Reviewed with test team on 16/10/2024
     * @throws IOException
     */
    @Test
    void test() throws IOException {
        // Given
        UiUtil.openResourceProjectForTest(driver, wait, "Chat/empty.py");

        // When
        LeftManuBar leftManuBar = new LeftManuBar(driver, wait);
        ChatWindow chatWindow = leftManuBar.openGeminiChat();
        chatWindow.stepInto();
        chatWindow.checkIfProjectIsElectedAndSelectIfNot();
        chatWindow.closeTipsIfVisible();

        WebElement chatInputField = chatWindow.getChatInputField();
        chatInputField.sendKeys("write a function to add two numbers and return the result");
        chatWindow.getSubmitButton().click();

        List<WebElement> chatHistory = chatWindow.waitUntilChatFinishAndGetHistory();
        assertTrue(chatHistory.size() >= 2, "Chat history div does not exist");

        TimeUtil.doDelayInSeconds(2);
        WebElement insertCode = findElement(wait, By.xpath("//*[@aria-label='insert']"));
        insertCode.click();
        TimeUtil.doDelayInSeconds(2);
        chatWindow.stepOut();

        WebElement editorWindow = wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("//*[contains(@class,'editor-scrollable')]")));
        editorWindow.click();

        // Then
        assertFalse(editorWindow.getText().isEmpty(), "Code generated from chat does not insert into the file");
    }

}