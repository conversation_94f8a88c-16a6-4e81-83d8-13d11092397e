package com.cienet.ide.test.common.vo;

public class TestSummary {

    public enum TestResult {
        Successful,
        Failed,
        Aborted
    }

    private String testName;

    private TestResult testResult;

    private String ideVersion;

    private String pluginVersion;

    private String agentLog;

    private String screenShot;

    private long executionTime;

    private String exceptionMessage;

    public String getAgentLog() {
        return agentLog;
    }

    public void setAgentLog(String agentLog) {
        this.agentLog = agentLog;
    }

    public String getExceptionMessage() {
        return exceptionMessage;
    }

    public void setExceptionMessage(String exceptionMessage) {
        this.exceptionMessage = exceptionMessage;
    }

    public long getExecutionTime() {
        return executionTime;
    }

    public void setExecutionTime(long executionTime) {
        this.executionTime = executionTime;
    }

    public String getScreenShot() {
        return screenShot;
    }

    public void setScreenShot(String screenShot) {
        this.screenShot = screenShot;
    }

    public String getTestName() {
        return testName;
    }

    public void setTestName(String testName) {
        this.testName = testName;
    }

    public String getIdeVersion() {
        return ideVersion;
    }

    public void setIdeVersion(String ideVersion) {
        this.ideVersion = ideVersion;
    }

    public String getPluginVersion() {
        return pluginVersion;
    }

    public void setPluginVersion(String pluginVersion) {
        this.pluginVersion = pluginVersion;
    }

    public TestResult getTestResult() {
        return testResult;
    }

    public void setTestResult(TestResult testResult) {
        this.testResult = testResult;
    }
}
