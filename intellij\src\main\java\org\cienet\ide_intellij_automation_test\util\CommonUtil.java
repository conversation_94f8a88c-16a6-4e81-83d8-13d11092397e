package org.cienet.ide_intellij_automation_test.util;

import com.intellij.remoterobot.RemoteRobot;
import com.intellij.remoterobot.fixtures.EditorFixture;
import com.intellij.remoterobot.utils.Keyboard;
import static com.intellij.remoterobot.search.locators.Locators.byXpath;
import java.time.Duration;
import java.awt.Toolkit;
import java.awt.datatransfer.StringSelection;
import java.awt.event.KeyEvent;
import com.intellij.remoterobot.fixtures.JLabelFixture;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CommonUtil {

    private static final Logger log = LoggerFactory.getLogger(CommonUtil.class);
    public static String getCurrentDir() {
        return System.getProperty("user.dir");
    }

    public static void validateIsMac() {
        if (!System.getProperty("os.name").toLowerCase().contains("mac")) {
            throw new UnsupportedOperationException("This test only runs on macOS.");
        }
    }

    public static void enterTextByLines(Keyboard keyboard, String text) {
        String[] lines = text.split("\n");
        for (int i = 0; i < lines.length; i++) {
            keyboard.enterText(lines[i], 5);
            if (i < lines.length - 1) {
                keyboard.enter();
            }
        }
    }

    /**
     * Set entire text content at once using clipboard and paste
     * This method handles newlines properly
     */
    public static void setTextUsingClipboard(Keyboard keyboard, String text) {
        // Copy text to clipboard
        StringSelection stringSelection = new StringSelection(text);
        Toolkit.getDefaultToolkit().getSystemClipboard().setContents(stringSelection, null);

        // Small delay to ensure clipboard is set
        TimeUtil.doDelay(100);

        // Paste using Ctrl+V (or Cmd+V on Mac)
        if (System.getProperty("os.name").toLowerCase().contains("mac")) {
            keyboard.hotKey(KeyEvent.VK_META, KeyEvent.VK_V);
        } else {
            keyboard.hotKey(KeyEvent.VK_CONTROL, KeyEvent.VK_V);
        }
    }

    public static EditorFixture prepareEditor(RemoteRobot remoteRobot) {
        EditorFixture editor = remoteRobot.find(
                EditorFixture.class,
                byXpath("//div[@class='EditorComponentImpl']"),
                Duration.ofSeconds(30));
        editor.setText("");
        editor.click();

        return editor;
    }    
    
    //Wait for code generation and refocus editor
    public static void waitAndRefocusEditor(Keyboard keyboard, EditorFixture editor, int delayMs) {
        TimeUtil.doDelay(delayMs);
        keyboard.key(KeyEvent.VK_TAB);
        editor.click();
    }

    //Cleanup editor after test completion
    public static void cleanupEditor(RemoteRobot remoteRobot, int delayMs) {
        TimeUtil.doDelay(delayMs);
        prepareEditor(remoteRobot);
    }
    
    /**
     * Checks if AI completion dialog is visible with "to complete" text.
     * @return true if dialog found within 5 seconds, false otherwise
     */
    public static boolean hasCompletionDialog(RemoteRobot remoteRobot) {
        boolean hasCompletionDialog = false;

        try {
            JLabelFixture tabToCompleteLabel = remoteRobot.find(
                    JLabelFixture.class,
                    byXpath("//div[@class='DslLabel']"),
                    Duration.ofSeconds(3));

            hasCompletionDialog = tabToCompleteLabel.getValue().contains("to complete");
        } catch (Exception e) {
            log.info("Completion dialog not found.");
        }

        return hasCompletionDialog;
    }

    /**
     * Triggers AI code generation via Alt+G hotkey and waits for completion.
     * 
     * @param keyboard the Keyboard instance to send the hotkey
     * @param duration the delay duration in milliseconds to wait after triggering
     */
    public static void triggerCodeGeneration(RemoteRobot remoteRobot, Keyboard keyboard, int duration) {
        keyboard.hotKey(KeyEvent.VK_ALT, KeyEvent.VK_G);
        IntellijUtil.waitShowCodeSuggestion(remoteRobot, duration);
    }
}
