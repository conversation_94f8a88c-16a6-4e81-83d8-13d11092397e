package org.cienet.ide_intellij_automation_test.test.codeGeneration;

import org.cienet.ide_intellij_automation_test.util.TimeUtil;
import org.cienet.ide_intellij_automation_test.util.CommonUtil;
import org.cienet.ide_intellij_automation_test.util.TestExceptionHandler;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import com.intellij.remoterobot.fixtures.EditorFixture;
import com.intellij.remoterobot.utils.Keyboard;

import java.io.IOException;
import java.awt.event.KeyEvent;

import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_75_Test extends BaseGenerationTest {

    private static final int GENERATION_WAIT_TIME_MS = 5000;
    private static final int CLEANUP_DELAY_MS = 5000;

    @Test
    void Gemini_75() throws IOException {
        closeAllCurrentTabs();
        openResourceProjectForTest("Code Completion/empty.py");
        
        String prompt = """
            #Given two numbers, write a function to calculate their sum
            def add(a, b : int) -> int:
            """;

        EditorFixture editor = CommonUtil.prepareEditor(remoteRobot);
        Keyboard keyboard = new Keyboard(remoteRobot);

        // Enter prompt and add newline
        CommonUtil.enterTextByLines(keyboard, prompt);
        keyboard.enter();

        int initialTextLength = editor.getText().length();

        // Wait for code generation
        CommonUtil.waitAndRefocusEditor(keyboard, editor, GENERATION_WAIT_TIME_MS);

        // Verify code generation
        String generatedText = editor.getText();
        int generatedTextLength = generatedText.trim().length();

        assertTrue(
            generatedTextLength > initialTextLength,
            String.format("Code generation failed. Generated text:\n%s\n", generatedText)
        );
          // Cleanup: ensure editor is empty before closing
        CommonUtil.cleanupEditor(remoteRobot, CLEANUP_DELAY_MS);
    }    
}
