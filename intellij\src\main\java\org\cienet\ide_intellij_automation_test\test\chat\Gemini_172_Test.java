package org.cienet.ide_intellij_automation_test.test.chat;

import com.intellij.remoterobot.fixtures.CommonContainerFixture;
import com.intellij.remoterobot.fixtures.EditorFixture;
import com.intellij.remoterobot.fixtures.JButtonFixture;
import com.intellij.remoterobot.fixtures.JPopupMenuFixture;
import com.intellij.remoterobot.fixtures.JTextFieldFixture;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.Point;
import java.io.IOException;
import java.time.Duration;
import static com.intellij.remoterobot.search.locators.Locators.byXpath;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Test case for validating Gemini Code Assist's code explanation functionality.
 * This test verifies that the AI can properly explain selected Python code
 * segments.
 */
public class Gemini_172_Test extends BaseChatTest {
  private static final Logger log = LoggerFactory.getLogger(BaseChatTest.class);
  private static final int START_LINE = 2;
  private static final int END_LINE = 9;
  private static final Duration DEFAULT_TIMEOUT = Duration.ofSeconds(30);
  private static final Duration SHORT_TIMEOUT = Duration.ofSeconds(10);
  private static final Duration QUICK_TIMEOUT = Duration.ofSeconds(5);

  @Test
  protected void Gemini_172() throws IOException {
    // Open test file and initialize Gemini Code Assist
    openResourceProjectForTest("Chat/two_sum.py");
    CommonContainerFixture gcaWindow = openGeminiCodeAssistWindow();
    clearChatHistory(gcaWindow);

    // Select code and request explanation
    EditorFixture editor = getEditor();
    requestCodeExplanation(editor);

    // Verify explanation content
    verifyExplanationResponse();
  }

  /**
   * Opens the Gemini Code Assist window, creating it if not already present
   */
  private CommonContainerFixture openGeminiCodeAssistWindow() {
    try {
      return findGCAWindow();
    } catch (Exception e) {
      openGCAToolWindow();
      return findGCAWindow();
    }
  }

  private CommonContainerFixture findGCAWindow() {
    return remoteRobot.find(CommonContainerFixture.class,
        byXpath("//div[@class='ToolWindowHeader'][.//div[@text='Gemini Code Assist']]"), SHORT_TIMEOUT);
  }

  private void openGCAToolWindow() {
    remoteRobot.find(JButtonFixture.class, byXpath("//div[@myicon='bot-toolwindow.svg']"), SHORT_TIMEOUT).click();
  }

  /**
   * Clears existing chat history if any exists
   */
  private void clearChatHistory(CommonContainerFixture toolWindowHeader) {
    toolWindowHeader.click();
    try {
      toolWindowHeader.find(CommonContainerFixture.class, byXpath("//div[@myicon='delete.svg']"), QUICK_TIMEOUT)
          .click();
      remoteRobot.find(CommonContainerFixture.class,
          byXpath("//div[@text='Yes']"),
          QUICK_TIMEOUT).click();
    } catch (Exception e) {
      log.info("No chat history to clear");
    }
  }

  private EditorFixture getEditor() {
    EditorFixture editor = remoteRobot.find(EditorFixture.class, byXpath("//div[@class='EditorComponentImpl']"),
        DEFAULT_TIMEOUT);
    editor.click();
    return editor;
  }

  /**
   * Selects code and triggers Gemini's explanation feature via context menu
   */
  private void requestCodeExplanation(EditorFixture editor) {
    selectLines(START_LINE, END_LINE);
    editor.rightClick(new Point(100, 100));

    JPopupMenuFixture menu = remoteRobot.find(JPopupMenuFixture.class,
        byXpath("//div[@class='HeavyWeightWindow']"),
        DEFAULT_TIMEOUT);
    menu.findText("Gemini").click();

    CommonContainerFixture action = remoteRobot.find(CommonContainerFixture.class,
        byXpath("//div[@text='Gemini']//div[@text='Explain This']"), SHORT_TIMEOUT);
    action.click();
  }

  /**
   * Verifies that Gemini's explanation contains expected content
   */
  private void verifyExplanationResponse() {
    CommonContainerFixture gcaToolWindow = remoteRobot.find(CommonContainerFixture.class,
        byXpath("//div[@accessiblename='Gemini Code Assist Tool Window']"), SHORT_TIMEOUT);

    // Wait for response completion
    gcaToolWindow.find(CommonContainerFixture.class, byXpath("//div[@class='FeedbackButtons'] "), DEFAULT_TIMEOUT);

    CommonContainerFixture botResponse = gcaToolWindow.find(CommonContainerFixture.class,
        byXpath("//div[@class='MessagePanel'][.//div[@class='FeedbackButtons']]"),
        SHORT_TIMEOUT);

    var textPanes = botResponse.findAll(JTextFieldFixture.class, byXpath("//div[@class='SelectableTextPane']"));

    boolean containsExplanation = textPanes.stream().anyMatch(pane -> pane.getText().contains("break down"));

    assertTrue(containsExplanation, "Explanation should contain a breakdown of the code");
  }
}