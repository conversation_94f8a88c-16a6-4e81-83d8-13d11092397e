package org.cienet.ide_intellij_automation_test.test.codeTransform;

import org.cienet.ide_intellij_automation_test.test.codeGeneration.BaseGenerationTest;
import org.cienet.ide_intellij_automation_test.util.CommonUtil;
import org.cienet.ide_intellij_automation_test.util.TestExceptionHandler;
import org.cienet.ide_intellij_automation_test.util.TimeUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import com.intellij.remoterobot.fixtures.CommonContainerFixture;
import com.intellij.remoterobot.fixtures.EditorFixture;
import com.intellij.remoterobot.fixtures.JPopupMenuFixture;
import com.intellij.remoterobot.fixtures.JListFixture;
import com.intellij.remoterobot.utils.Keyboard;

import java.io.IOException;
import java.time.Duration;
import java.awt.event.KeyEvent;

import com.cienet.ide.test.common.constants.FileNameTest;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static com.intellij.remoterobot.search.locators.Locators.byXpath;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_645_Test extends BaseGenerationTest {

    private static final int CLEANUP_DELAY_MS = 5000;
    private static final int PROMPT_WAIT_TIME_MS = 1000;
    private static final int FIX_PROMPT_WAIT_TIME_MS = 2000;
    private static final int HIGHLIGHT_BUTTON_WAIT_SECONDS = 30;
    private static final int CONTEXT_MENU_WAIT_SECONDS = 10;

    @Test
    void Gemini_645() throws IOException {
        // Setup
        openResourceProjectForTest(FileNameTest.EMPTY_JAVA);
        
        String originalCode = """
            //write a function to create a topic on gcp
            
            import com.google.api.gax.core.CredentialsProvider;
            
            import com.google.api.gax.core.FixedCredentialsProvider; 
            import com.google.auth.oauth2.GoogleCredentials;
            
            import com.google.cloud.pubsub.v1.TopicAdminClient; 
            import com.google.cloud.pubsub.v1.TopicAdminSettings; 
            import com.google.protobuf.Empty;
            
            // Initialize credentials 
            GoogleCredentials credentials = GoogleCredentials.fromStream(new FileInputStream(credentialsFilePath)) 
                .createScoped("https://www.googleapis.com/auth/cloud-platform"); 
            CredentialsProvider credentialsProvider = FixedCredentialsProvider.create(credentials);
            """;

        EditorFixture editor = CommonUtil.prepareEditor(remoteRobot);
        Keyboard keyboard = new Keyboard(remoteRobot);

        // Action - Enter code with syntax issues
        CommonUtil.enterTextByLines(keyboard, originalCode);

        int initialTextLength = editor.getText().length();

        // Trigger code generation
        triggerGeminiCodeFix(editor);

        // Wait for generation to complete
        TimeUtil.doDelay(CLEANUP_DELAY_MS);

        // Accept the generated fix
        var highlightButton = remoteRobot.find(
                CommonContainerFixture.class,
                byXpath("//div[@class='HighlightedDiffActionButton']"),
                Duration.ofSeconds(HIGHLIGHT_BUTTON_WAIT_SECONDS));
        highlightButton.click();

        // Verify code generation
        String generatedText = editor.getText();
        int generatedTextLength = generatedText.trim().length();

        assertTrue(
            generatedTextLength > initialTextLength,
            String.format("Code fix failed. Generated text:\n%s\n", generatedText)
        );        
    }

    /**
     * Triggers Gemini code fix through context menu
     */
    private void triggerGeminiCodeFix(EditorFixture editor) {
        editor.rightClick();
        
        JPopupMenuFixture contextMenu = remoteRobot.find(
                JPopupMenuFixture.class,
                byXpath("//div[@class='HeavyWeightWindow']"),
                Duration.ofSeconds(CONTEXT_MENU_WAIT_SECONDS));
        contextMenu.findText("Gemini").click();

        CommonContainerFixture generateCodeOption = remoteRobot.find(
                CommonContainerFixture.class,
                byXpath("//div[@text='Gemini']//div[@text='Open In-Editor Prompt...']"),
                Duration.ofSeconds(CONTEXT_MENU_WAIT_SECONDS));        
        generateCodeOption.click();

        // Wait for prompt menu to appear and enter fix command
        TimeUtil.doDelay(PROMPT_WAIT_TIME_MS);
        Keyboard keyboard = new Keyboard(remoteRobot);
        keyboard.key(KeyEvent.VK_DOWN);
        TimeUtil.doDelay(FIX_PROMPT_WAIT_TIME_MS);
        
        CommonUtil.enterTextByLines(keyboard, "missing package");
        TimeUtil.doDelay(FIX_PROMPT_WAIT_TIME_MS);
        keyboard.enter();
    }
}
