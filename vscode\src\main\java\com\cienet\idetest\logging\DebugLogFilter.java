package com.cienet.idetest.logging;

import ch.qos.logback.classic.PatternLayout;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.filter.Filter;
import ch.qos.logback.core.spi.FilterReply;
import com.cienet.idetest.util.CommonUtil;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.Buffer;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class DebugLogFilter extends Filter<ILoggingEvent> {

    private PatternLayout patternLayout;

    private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");

    private static final String LOG_FILE_NAME_PATTERN = "%s_%s.log";

    private static final ThreadLocal<String> logPathThreadLocal = new ThreadLocal<>();

    private static final ThreadLocal<BufferedWriter> logWritterThreadLocal = new ThreadLocal<>();

    public static void startTestLogging(String testName) {
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMATTER);
        String currentPath = CommonUtil.getCurrentDir();
        String logFileName = String.format(LOG_FILE_NAME_PATTERN, testName, timestamp);
        String logPath = currentPath + "/logs/" + logFileName;

        try {
            removeOldLogFiles(testName, currentPath);
            BufferedWriter writer = new BufferedWriter(new FileWriter(logPath, true));
            logWritterThreadLocal.set(writer);
            logPathThreadLocal.set(logPath);
        } catch (Exception e) {
            e.printStackTrace();
            // ignored
        }
    }

    public static void removeOldLogFiles(String testName, String currentPath) throws IOException {
        Path logDir = Paths.get(currentPath, "logs");
        Files.list(logDir)
                .filter(path -> path.getFileName().toString().matches(testName + "_\\d{8}_\\d{6}\\.log"))
                .forEach(path -> {
                    try {
                        Files.delete(path);
                    } catch (Exception e) {
                        e.printStackTrace(); // Handle the exception appropriately
                    }
                });
    }

    public static void stopTestLogging() {
        BufferedWriter writer = logWritterThreadLocal.get();
        try {
            writer.close();
        } catch (Exception e) {
            e.printStackTrace();
            // ignored
        }
        logWritterThreadLocal.remove();
    }

    public static String getLogPath() {
        return logPathThreadLocal.get();
    }

    @Override
    public void start() {
        super.start();
        // Initialize the pattern layout with the same pattern as in logback-spring.xml
        patternLayout = new PatternLayout();
        patternLayout.setContext(getContext());
        patternLayout.setPattern("%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36}:%line - %msg%n");
        patternLayout.start();
    }

    @Override
    public FilterReply decide(ILoggingEvent event) {
        // Format the log message using the pattern layout
        BufferedWriter writer = logWritterThreadLocal.get();

        if (writer != null) {
            try {
                String formattedMessage = patternLayout.doLayout(event);
                writer.write(formattedMessage);
                writer.flush();
            } catch (Exception e) {
                e.printStackTrace();
                // ignored
            }
        }

        return FilterReply.ACCEPT;
    }

    public static String getTestLogContent() {
        Path logFilePath = Paths.get(logPathThreadLocal.get());
        StringBuilder logContent = new StringBuilder();

        try (Stream<String> lines = Files.lines(logFilePath)) {
            for (String line : lines.collect(Collectors.toList())) {
                logContent.append(line).append(System.lineSeparator());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return logContent.toString();
    }
}
