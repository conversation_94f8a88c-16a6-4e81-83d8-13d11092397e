spring.application.name=ide-test-automation

#logging.file.name=logs/spring-boot-app.log
#logging.logback.rollingpolicy.file-name-pattern=${LOG_FILE}.%d{yyyy-MM-dd}.%i.gz
#logging.logback.rollingpolicy.max-file-size=10MB  # The maximum size of log file before it is archived.
#logging.logback.rollingpolicy.total-size-cap=0B # The maximum amount of size log archives can take before being deleted.
#logging.logback.rollingpolicy.max-history=10  # The maximum number of archive log files to keep (defaults to 7).

#logging.level.root=INFO
#logging.level.com.cienet.idetest=DEBUG

webdriver.chrome.driver=your-chrome-driver-path
uitest.google.project.id=ai-dev-preview-external
uitest.canonical.answer=your-canonical-answer-path
uitest.test.plan.resource=your-test-plan-resource-answer-path
uitest.osa.script=your-osa-script-path
