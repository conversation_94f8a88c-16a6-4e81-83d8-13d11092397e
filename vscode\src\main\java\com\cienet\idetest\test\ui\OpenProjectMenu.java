package com.cienet.idetest.test.ui;

import com.cienet.idetest.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.StaleElementReferenceException;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.support.ui.WebDriverWait;

import static com.cienet.idetest.util.UiUtil.findElement;

@Slf4j
public class OpenProjectMenu {

    private WebDriverWait wait;

    private WebDriver driver;

    public OpenProjectMenu(WebDriver driver, WebDriverWait wait) {
        this.driver = driver;
        this.wait = wait;
    }

    public void openProject() {
        StaleElementReferenceException e = null;

        for (int i = 0; i < 3; i++) {
            try {
                findElement(wait, "//a[@class='monaco-button monaco-text-button' and span='Open Folder']").click();
                return;
            } catch (StaleElementReferenceException se) {
                log.warn("Got StaleElementReferenceException! Will retry... i={}", i);
                e = se;
                TimeUtil.doDelay(500);
            }
        }

        throw e;
    }
}
