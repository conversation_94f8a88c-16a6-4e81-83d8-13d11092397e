on run { canonicalAnsPath, testplanResPath }
  main(canonicalAnsPath, testplanResPath)
end run

on main(canonicalAnsPath, testplanResPath)
  try
    tell application "System Events"
    -- Wait for the dialog to appear
      delay 2
      keystroke "/"

      -- Wait for a moment and remove the /
      delay 2
      key code 51 using {command down}

      -- Wait for a moment
      delay 1

      -- Type the path to the project folder
      set projectPath to canonicalAnsPath
      if projectPath is in {{}, {""}, ""} then
        set userName to (system attribute "USER")
        set projectPath to "/Users/" & userName & "/dev/GitHub/CIeNET/canonical-answer"
      end if
      keystroke projectPath

      -- Wait for a moment
      delay 1

      -- Press Enter to confirm the path
      keystroke return

      -- Wait for a moment
      delay 1

      -- Press En<PERSON> to open the folder
      keystroke return
    end tell

  on error errMsg number errNum
    log "errMsg=" & errMsg & ", errNum=" & errNum
  end try
end main
