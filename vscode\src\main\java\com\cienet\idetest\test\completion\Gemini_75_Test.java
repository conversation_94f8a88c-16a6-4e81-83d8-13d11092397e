package com.cienet.idetest.test.completion;

import com.cienet.idetest.test.ui.SuggestPreview;
import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;

import java.io.IOException;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
@ExtendWith(TestExceptionHandler.class)
public class Gemini_75_Test extends BaseCompletionTest {

    @Test
    public void test() throws IOException {
        // Given
        String filePath = "Code Completion/Python/empty.py";
        String comment = "# write a function to add two numbers";
        String funcDeclare = "def add(a, b : int) -> int:";

        // Trigger Suggest
        SuggestPreview suggestPreview = new SuggestPreview(driver, wait, actions);
        String promptText = suggestPreview.triggerSuggest(filePath, comment, funcDeclare);
        log.debug("promptText: {}", promptText);
        String suggestCode = suggestPreview.getSuggestCode();
        log.debug("suggestCode: {}", suggestCode);
        assertTrue(StringUtils.isNoneEmpty(suggestCode), "No code is suggested!");
    }

}
