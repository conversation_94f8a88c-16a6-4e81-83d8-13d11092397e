package org.cienet.ide_intellij_automation_test.test.codeCompletion;

import java.awt.event.KeyEvent;
import java.io.IOException;
import java.time.Duration;

import org.cienet.ide_intellij_automation_test.test.BaseTest;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.intellij.remoterobot.fixtures.EditorFixture;
import static com.intellij.remoterobot.search.locators.Locators.byXpath;
import com.intellij.remoterobot.utils.Keyboard;

public class BaseCompletionTest extends BaseTest {
    private static final Logger log = LoggerFactory.getLogger(BaseCompletionTest.class);

    protected void doSimpleCompletionTest(String filename, String fileContent) throws IOException {
        log.info("Start excute test");
        openResourceProjectForTest(filename);

        Keyboard keyboard = new Keyboard(remoteRobot);

        EditorFixture editor = remoteRobot.find(
                EditorFixture.class,
                byXpath("//div[@class='EditorComponentImpl']"),
                Duration.ofSeconds(30));

        editor.setText("");

        editor.click();
        String[] promptArray = fileContent.split("\\r?\\n|\\r");
        int index = 0;
        for (String line : promptArray) {
            keyboard.enterText(line, 20);
            index++;
            if (index < promptArray.length) {
                keyboard.enter();
            }
        }
        int promptLength = editor.getText().trim().length();
        remoteRobot.find(
                EditorFixture.class,
                byXpath("//div[contains(@visible_text, 'visibleText') and @class='EditorComponentImpl']".replace(
                        "visibleText",
                        "|| to complete")),
                Duration.ofSeconds(30));
        keyboard.key(KeyEvent.VK_TAB);
        String finalText = editor.getText();
        int finalLength = finalText.trim().length();
        assertTrue(finalLength > promptLength,
                String.format("Code completion failed. Prompt:\n%s\n", finalText));
    }

}
