package com.cienet.ide.test.common.util;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

import static java.lang.Thread.sleep;

public class TimeUtil {

    /**
     * The date time format string "yyyyMMddHHmmss" (y12s).
     */
    public static final String FORMAT_YYYYMMDD_HHMMSS = "yyyyMMdd_HHmmss";

    /**
     * Converts Date to String based on the specified format.
     *
     * @param date the given date to be converted
     * @param format the given format string (please
     *        @see java.text.SimpleDateFromat for detailed information on legal formats)
     * @return String object of the given format, or null if the given date is null.
     */
    public static String date2String(Date date, String format) {
        if (date == null) {
            return null;
        }
        DateFormat fmt = new SimpleDateFormat(format);
        return fmt.format(date);
    }

    /**
     * Converts Date to String of yyyyMMdd_HHmmss (y13s) format.
     *
     * @param date the given date object
     */
    public static String dateToY13s(Date date){
        return date2String(date, FORMAT_YYYYMMDD_HHMMSS);
    }

    public static void doDelay(long millis) {
        try {
            sleep(millis);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    public static void doDelayInSeconds(int seconds) {
        doDelay(seconds * 1_000L);
    }

    public static void doDelayInMinutes(int minutes) {
        try {
            sleep(minutes * 60000L);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    // delay without Exception
    public static void doNoIntrDelay(long millis) {
        try {
            sleep(millis);
        } catch (InterruptedException e) {
            // ignore
        }
    }

}
