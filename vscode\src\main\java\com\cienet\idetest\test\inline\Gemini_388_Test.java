package com.cienet.idetest.test.inline;

import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.WebElement;

import java.io.IOException;

@Slf4j
@ExtendWith(TestExceptionHandler.class)
public class Gemini_388_Test extends BaseInlineTest {

    @Test
    public void testInlineChat() throws IOException {
        UiUtil.openResourceProjectForTest(driver, wait, "Chat/empty.py");
        TimeUtil.doDelay(2000);
        WebElement element = triggerInlineChat(true);
        String textBefore = element.getAttribute("value");
        log.debug("Text value before: {}", textBefore);
        element.sendKeys("Explain it");
        TimeUtil.doDelay(3000);
        String textAfter = element.getAttribute("value");
        log.debug("Text value after: {}", textAfter);
        Assertions.assertTrue(textAfter.length() > textBefore.length());
    }

}
