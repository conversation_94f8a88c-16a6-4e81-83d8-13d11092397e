import org.jetbrains.intellij.platform.gradle.IntelliJPlatformType

plugins {
    java
    id("org.springframework.boot") version "3.4.4"
    id("io.spring.dependency-management") version "1.1.7"
    id("org.jetbrains.intellij.platform") version ("2.2.1")
}

group = "org.cienet"
version = "0.0.1-SNAPSHOT"
val platformVersion = System.getenv("IDEA_VERSION")
    ?: providers.gradleProperty("ideaVersion").get()

println("Using IDEA version: $platformVersion")


java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

repositories {
    mavenCentral()
    intellijPlatform {
        defaultRepositories()
    }
    maven {
        url = uri("https://packages.jetbrains.team/maven/p/ij/intellij-dependencies")
    }
}

dependencies {
    // Shared library with common classes
    implementation("org.cienet:ide-test-shared")

    intellijPlatform {
        create(IntelliJPlatformType.IntellijIdeaUltimate, platformVersion)
    }
    implementation("com.intellij.remoterobot:remote-robot:0.11.22")
    implementation("com.intellij.remoterobot:remote-fixtures:0.11.22")
    implementation("org.jetbrains.kotlin:kotlin-reflect:2.1.0")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:2.3.0")
    developmentOnly("org.springframework.boot:spring-boot-devtools")

    implementation("org.reflections:reflections:0.10.2")

    testImplementation("org.springframework.boot:spring-boot-starter-test")
    implementation("org.junit.jupiter:junit-jupiter-api")
    implementation("org.junit.platform:junit-platform-launcher")
    testImplementation("junit:junit:4.13.2")
    implementation("junit:junit:4.13.2")

    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine")
    implementation("org.junit.jupiter:junit-jupiter-engine")

    compileOnly("org.projectlombok:lombok:1.18.32")
    annotationProcessor("org.projectlombok:lombok:1.18.32")
}

tasks.withType<Test> {
    useJUnitPlatform()
}

tasks.bootRun {
    jvmArgs(
        "--add-opens=java.base/java.lang=ALL-UNNAMED",
        "--add-opens=java.base/java.util=ALL-UNNAMED"
    )
    systemProperty("ideaVersion", platformVersion)
}

tasks.named("prepareTestSandbox") {
    dependsOn(tasks.named("bootJar"))
}

tasks.jar {
    enabled = false
}

tasks.named("instrumentedJar") {
    dependsOn(tasks.named("bootJar"))
}

tasks.bootJar {
    archiveBaseName.set("ide_intellij_automation_test")
    archiveVersion.set("0.0.1-SNAPSHOT-base")
}

// https://plugins.jetbrains.com/docs/intellij/tools-intellij-platform-gradle-plugin-tasks.html#runIdeForUiTests
val runIdeForUiTests by intellijPlatformTesting.runIde.registering {
    task {
        jvmArgumentProviders += CommandLineArgumentProvider {
            listOf(
                "-Dide.mac.message.dialogs.as.sheets=false",
                "-Djb.privacy.policy.text=<!--999.999-->",
                "-Djb.consents.confirmation.enabled=false",
                "-Dide.mac.file.chooser.native=false",
                "-DjbScreenMenuBar.enabled=false",
                "-Dapple.laf.useScreenMenuBar=false",
                "-Didea.trust.all.projects=true",
                "-Dide.show.tips.on.startup.default.value=false",
                // "-Drobot-server.host.public=true",
                // "--add-opens=java.base/java.lang=ALL-UNNAMED"
            )
        }
    }
    plugins {
        robotServerPlugin()
    }
}

tasks.register("prodTar", Tar::class) {
    archiveFileName.set("intellij.agent.tar.gz")
    destinationDirectory.set(layout.buildDirectory.dir("tar"))
    compression = Compression.GZIP

    // Include files matching startagent.*
    from(".") {
        include("startagent.*")
        include("agentenv.*")
        include("runide.gradle.kts")
        include("gradlew")
        include("gradlew.bat")
        include("gradle/**")
    }

    // Include jar files from build/libs
    from("build/libs") {
        include("*-base.jar")
        into("libs")
    }

    // Include ../test-plan-resource into a subdirectory named "test-plan-resource"
    from("../../test-plan-resource") {
        exclude(".git/**")
        into("test-plan-resource")
    }

    // Add settings.gradle.kts dynamically
    val tempDir = layout.buildDirectory.dir("temp").get().asFile
    val settingsFile = File(tempDir, "settings.gradle.kts")
    doFirst {
        tempDir.mkdirs()
        settingsFile.writeText("rootProject.name = \"ide_intellij_automation_test\"\n")
    }
    from(settingsFile) {
        into(".")
    }
}.configure {
    dependsOn(tasks.named("build"))
}