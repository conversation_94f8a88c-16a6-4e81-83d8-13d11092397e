package com.cienet.idetest.test.chat;

import com.cienet.idetest.test.ui.BottomBar;
import com.cienet.idetest.test.ui.ChatWindow;
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.WebElement;

import static org.junit.jupiter.api.Assertions.assertFalse;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_164_Test extends BaseChatTest {

    /**
     * Gemini-164 : Clear conversation validation - Reset chat - Version3
     * To validate if the conversation is cleared and user can start a new conversation
     * with <PERSON><PERSON> and new conversations will not be affected by previously cleared conversations.
     *
     * Test compared with TesLink on: 3/10/2024
     * Reviewed with test team on 16/10/2024
     */
    @Test
    void test() {
        // Given
        BottomBar bottomBar = new BottomBar(driver, wait);
        ChatWindow chatWindow = bottomBar.openChatWindow();

        // When
        chatWindow.stepInto();
        chatWindow.closeTipsIfVisible();
        WebElement chatInputField = chatWindow.getChatInputField();
        chatInputField.sendKeys("Give me code to solve a two sum problem");
        chatWindow.getSubmitButton().click();
        WebElement chatHistory = chatWindow.waitUntilChatFinish();
        chatHistory.isDisplayed();
        chatWindow.stepOut();
        TimeUtil.doDelayInSeconds(3);

        chatWindow.clearChatButton().click();
        chatWindow.stepInto();
        chatInputField.sendKeys("How do I make it more efficient?");
        chatWindow.getSubmitButton().click();
        WebElement secondChatHistory = chatWindow.waitUntilChatFinish();
        secondChatHistory.isDisplayed();

        // Then
        assertFalse(secondChatHistory.getText().contains("two sum"), "Response from follow up question should not be related to earlier cleared conversation");
        assertFalse(secondChatHistory.getText().contains("two_sum"), "Response from follow up question should not be related to earlier cleared conversation");
    }

}
