package org.cienet.ide_intellij_automation_test.test;

import org.apache.commons.lang3.StringUtils;
import org.cienet.ide_intellij_automation_test.logging.DebugLogFilter;
import org.cienet.ide_intellij_automation_test.util.PluginVersionUtil;
import org.cienet.ide_intellij_automation_test.util.SessionUtil;
import org.cienet.ide_intellij_automation_test.util.IdeVersionUtil;

import com.cienet.ide.test.common.util.ScreenRecordHelper;
import com.cienet.ide.test.common.vo.VersionInfo;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.TestInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.redhat.devtools.intellij.commonuitest.AbstractLibraryBaseTest;

/**
 * Base test class for IntelliJ automation tests with agentLog support
 * This class handles the lifecycle of test logging and Remote Robot integration
 */
public class BaseTest extends AbstractLibraryBaseTest {
    private Boolean videoEnabled;
    private ScreenRecordHelper recordHelper;
    
    private static final Logger log = LoggerFactory.getLogger(BaseTest.class);
    private static VersionInfo versionInfo = null;

    @BeforeEach
    protected void setup(TestInfo testInfo) {
        // Use actual test method name for logging
        String testName = testInfo.getTestMethod().isPresent() 
            ? testInfo.getTestMethod().get().getName() 
            : getClass().getSimpleName();
        
        long threadId = Thread.currentThread().getId();
        
        log.info("Starting test method: {} from class: {} on thread: {}", 
                testName, getClass().getSimpleName(), threadId);
        
        // Start agent logging for this specific test method
        DebugLogFilter.startTestLogging(testName);

        if (isVideoEnabled()) {
            recordHelper = new ScreenRecordHelper();
            try {
                recordHelper.startRecord(testName, SessionUtil.getResultOid());
                log.info("Record started for test method: {}", testName);
            } catch (Exception e) {
                log.error("Failed to start screen record.", e);
                // ignored
            }
        }

        
        log.info("Agent logging started for test method: {}", testName);
        
        // Get version info if not already cached
        if (versionInfo == null) {
            versionInfo = getIdePluginVersion();
        }
        closeAllCurrentTabs();
        checkGeminiActiveOrFail();
    }
    
    protected boolean isVideoEnabled() {
        if (videoEnabled != null) {
            return videoEnabled;
        }
        String enableScreeRecord = System.getProperty("uitest.enable.screen.record");
        // Enable screen record by default.
        boolean enabled = true;

        if (StringUtils.isNotEmpty(enableScreeRecord)) {
            enabled = Boolean.valueOf(enableScreeRecord);
        }

        log.info("Enable screen record: {}.", enabled);
        videoEnabled = enabled;
        return videoEnabled;
    }

    protected VersionInfo getIdePluginVersion() {
        if (versionInfo != null && versionInfo.bothValuePresented()) {
            log.debug("Using cached version info: {}", versionInfo);
            return versionInfo;
        }
        
        try {
            log.debug("Retrieving IDE and plugin version information...");
            
            var ideVersion = IdeVersionUtil.getIdeVersion(remoteRobot);
            var pluginVersion = PluginVersionUtil.getGeminiPluginVersion(remoteRobot);
            
            if (ideVersion != null && pluginVersion != null) {
                versionInfo = new VersionInfo(ideVersion.toString(), pluginVersion);
                log.info("Version info created - IDE: {}, Plugin: {}", 
                    ideVersion.toString(), pluginVersion);
            } else {
                log.warn("Unable to retrieve complete version information - IDE: {}, Plugin: {}", 
                    ideVersion, pluginVersion);
            }
            
            log.info("Got versionInfo: {} from telemetry.", versionInfo);
            return versionInfo;
            
        } catch (Exception e) {
            log.error("Failed to retrieve version information", e);
            return null;
        }
    }

    /**
     * Static getter method for returning VersionInfo to the client
     */
    public static VersionInfo getVersionInfo() {
        return versionInfo;
    }

    @AfterEach
    protected void tearDown(TestInfo testInfo) {
        // Use actual test method name for logging
        String testName = testInfo.getTestMethod().isPresent() 
            ? testInfo.getTestMethod().get().getName() 
            : getClass().getSimpleName();
        
        long threadId = Thread.currentThread().getId();
        
        log.info("Finishing test method: {} from class: {} on thread: {}", 
                testName, getClass().getSimpleName(), threadId);
        
        // Stop agent logging and cleanup resources
        try {
            DebugLogFilter.stopTestLogging();

            if (recordHelper != null) {
                try {
                    recordHelper.stopRecord();
                    log.info("Record stopped for test method: {}", testName);
                } catch (Exception e) {
                    log.error("Failed to stop screen record.", e);
                    // ignored
                }
            }
            log.info("Agent logging stopped for test method: {}", testName);
        } catch (Exception e) {
            log.error("Error stopping agent logging for test: {}: {}", testName, e.getMessage());
        }
    }
} 