package com.cienet.idetest.test.generation;

import com.cienet.idetest.util.TestExceptionHandler;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.IOException;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_6_Test extends BaseGenerationTest {

    @Test
    void testCodeGeneration() throws IOException {
        String prompt = "# write a function that checks if input is an even number\n\n"
                + "def is_even(x):\n";
        String filePath = "Code Generation/Python/is_even.py";
        testCodeGenByText(filePath, prompt);
    }
}
