package org.cienet.ide_intellij_automation_test.test.codeGeneration;

import com.intellij.remoterobot.fixtures.CommonContainerFixture;
import com.intellij.remoterobot.fixtures.EditorFixture;
import com.intellij.remoterobot.fixtures.JPopupMenuFixture;
import com.intellij.remoterobot.fixtures.JTextFieldFixture;
import com.intellij.remoterobot.utils.Keyboard;
import org.cienet.ide_intellij_automation_test.util.CommonUtil;
import org.cienet.ide_intellij_automation_test.util.TestExceptionHandler;
import org.cienet.ide_intellij_automation_test.util.TimeUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.IOException;
import java.time.Duration;

import static com.intellij.remoterobot.search.locators.Locators.byXpath;
import static org.junit.jupiter.api.Assertions.assertTrue;


@ExtendWith(TestExceptionHandler.class)
public class Gemini_584_Test extends BaseGenerationTest {
    
    private static final int DEFAULT_TIMEOUT_SECONDS = 10;
    private static final int EXTENDED_TIMEOUT_SECONDS = 30;
    private static final int DELAY_MILLISECONDS = 5000;
    
    @Test
    void Gemini_584() throws IOException {
        // Navigate to the following file: Google-Gemini → UI-Function → Chat -> empty.py
        closeAllCurrentTabs();
        openMultiplesFiles("Chat/empty.py");

        EditorFixture editor = CommonUtil.prepareEditor(remoteRobot);
        Keyboard keyboard = new Keyboard(remoteRobot);

        // Type a natural language prompt related to code generation and press Enter to submit the request.
        openGeminiInEditorPrompt(editor);
        keyboard.enterText("generate a quick sort function", 10);

        int startLength = editor.getText().length();
        keyboard.enter();

        // Click diff button and close the diff view
        acceptGeneratedCode();

        // The user is redirected to the sidebar chat
        openGeminiCodeAssistSidebar();
        
        // Type a natural language prompt
        openGeminiInEditorPrompt(editor);
        keyboard.enterText("explain what my code does", 10);

        // The prompt is automatically sent to the chat.
        keyboard.enter();

        TimeUtil.doDelay(DELAY_MILLISECONDS);
        editor.click();

        String finalText = editor.getText();
        int finalLength = finalText.trim().length();

        assertTrue(finalLength != startLength,
                String.format("Code transform failed. Prompt:\n%s\n", finalText));
    }

    private void openGeminiInEditorPrompt(EditorFixture editor) {
        editor.rightClick();

        var menu = remoteRobot.find(
                JPopupMenuFixture.class,
                byXpath("//div[@class='HeavyWeightWindow']"),
                Duration.ofSeconds(DEFAULT_TIMEOUT_SECONDS));
        menu.findText("Gemini").click();

        var generateMenu = remoteRobot.find(
                CommonContainerFixture.class,
                byXpath("//div[@text='Gemini']//div[@text='Open In-Editor Prompt...']"),
                Duration.ofSeconds(DEFAULT_TIMEOUT_SECONDS));
        generateMenu.click();

        var jbTextField = remoteRobot.find(
                JTextFieldFixture.class,
                byXpath("//div[@visible_text='visibleText' and @class='JBTextField']".replace("visibleText",
                        "Type / for commands or chat with Gemini Code Assist")),
                Duration.ofSeconds(DEFAULT_TIMEOUT_SECONDS));
        jbTextField.click();
    }

    private void acceptGeneratedCode() {
        var highlightButton = remoteRobot.find(
                CommonContainerFixture.class,
                byXpath("//div[@class='HighlightedDiffActionButton']"),
                Duration.ofSeconds(EXTENDED_TIMEOUT_SECONDS));
        highlightButton.click();
    }

    private void openGeminiCodeAssistSidebar() {
        var geminiCodeAssistButton = remoteRobot.find(
                CommonContainerFixture.class,
                byXpath("//div[@class='ToolWindowRightToolbar']//div[@class='SquareStripeButton' and contains(@myaction, 'Gemini Code Assist')]"),
                Duration.ofSeconds(DEFAULT_TIMEOUT_SECONDS));
        geminiCodeAssistButton.click();
    }
}
