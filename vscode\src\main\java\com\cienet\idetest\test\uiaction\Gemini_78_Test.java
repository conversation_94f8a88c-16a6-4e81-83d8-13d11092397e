package com.cienet.idetest.test.uiaction;

// import org.junit.jupiter.api.AfterEach; // Removed
// import org.junit.jupiter.api.BeforeEach; // Removed
import org.junit.jupiter.api.Test;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
// import org.openqa.selenium.WebDriver; // Removed
import org.openqa.selenium.WebElement;
// import org.openqa.selenium.interactions.Actions; // Removed
// import org.openqa.selenium.support.ui.WebDriverWait; // Removed

// import java.io.IOException; // Removed
// import java.time.Duration; // Removed

import static java.lang.Thread.sleep; // Used in test()
import static com.cienet.idetest.util.UiUtil.findElement; // Used in test()
// import static com.cienet.idetest.util.UiUtil.prepareTestWithOpenProject; // Removed
import static org.junit.jupiter.api.Assertions.assertTrue; // Used in test()

import com.cienet.idetest.test.uiaction.BaseUiActionTest;

public class Gemini_78_Test extends BaseUiActionTest {

    // Fields driver, wait, actions are inherited from BaseUiActionTest
    // setup() method is inherited from BaseUiActionTest

    @Test
    public void test() throws InterruptedException {
        // Given
        actions.keyDown(Keys.LEFT_SHIFT).keyDown(Keys.COMMAND).sendKeys("p").keyUp(Keys.LEFT_SHIFT).keyUp(Keys.COMMAND).perform();

        WebElement quickMenu = findElement(wait, By.cssSelector("[aria-describedby='quickInput_message']"));
        quickMenu.sendKeys("Open User Settings (JSON)");
        actions.sendKeys(Keys.ENTER).perform();
        sleep(1000);
        actions.sendKeys(Keys.ARROW_RIGHT).perform();
        actions.sendKeys("\"cloudcode.updateChannel\": \"trusted-testers\",").perform();
        actions.keyDown(Keys.CONTROL).sendKeys("s").keyUp(Keys.CONTROL).perform();

        // When
        WebElement extensionsButton = findElement(wait, By.className("codicon-extensions-view-icon"));
        extensionsButton.click();
        sleep(4000);
        WebElement cloudConsoleExtension = findElement(wait, By.xpath("//*[contains(@aria-label, 'Gemini Code Assist + Google Cloud Code')]"));
        cloudConsoleExtension.click();
        extensionsButton.click();
        WebElement geminiStarIcon = findElement(wait, By.xpath("//a[@role='button' and @aria-label='material-spark']"));
        geminiStarIcon.click();
        WebElement element = findElement(wait, By.xpath("//*[contains(@aria-label, 'Select Google Cloud project')]"));
        element.click();
        WebElement googleProject = findElement(wait, By.xpath("//*[contains(@aria-label, 'aemon-projects-dev-021')]"));
        googleProject.click();

        // then
        assertTrue(geminiStarIcon.isDisplayed());
    }

    // tearDown() method is inherited from BaseTest (via BaseUiActionTest)
    // and handles driver.quit()

}
