package org.cienet.ide_intellij_automation_test.util;

import com.intellij.remoterobot.RemoteRobot;
import com.intellij.remoterobot.fixtures.CommonContainerFixture;
import com.intellij.remoterobot.fixtures.ComponentFixture;
import com.intellij.remoterobot.fixtures.JButtonFixture;
import com.intellij.remoterobot.fixtures.JTextFieldFixture;
import com.intellij.remoterobot.fixtures.JTreeFixture;
import com.intellij.remoterobot.fixtures.JLabelFixture;
import com.intellij.remoterobot.fixtures.JListFixture;
import com.intellij.remoterobot.utils.Keyboard;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.awt.event.KeyEvent;

import static com.intellij.remoterobot.search.locators.Locators.byXpath;

/**
 * Utility class for retrieving plugin version information via Remote Robot
 */
public class PluginVersionUtil {

    private static final Logger log = LoggerFactory.getLogger(PluginVersionUtil.class);
    private static final Duration DEFAULT_TIMEOUT = Duration.ofSeconds(30);
    private static final Duration SHORT_TIMEOUT = Duration.ofSeconds(10);

    // Plugin name constants
    private static final String GEMINI_PLUGIN_NAME = "Gemini Code Assist";

    /**
     * Get Gemini Code Assist plugin version using Remote Robot
     * 
     * @param remoteRobot Remote Robot instance
     * @return Plugin version string or null if not found
     */
    public static String getGeminiPluginVersion(RemoteRobot remoteRobot) {
        if (remoteRobot == null) {
            log.warn("RemoteRobot is null, cannot retrieve plugin version");
            return null;
        }

        try {
            log.info("Attempting to retrieve Gemini Code Assist plugin version...");

            // Method 1: Try to get from Settings > Plugins
            String version = getVersionFromPluginSettings(remoteRobot);
            if (version != null) {
                log.info("Successfully retrieved plugin version: {}", version);
                return version;
            }

            log.warn("Could not retrieve Gemini Code Assist plugin version");
            return null;

        } catch (Exception e) {
            log.error("Error retrieving Gemini Code Assist plugin version", e);
            return null;
        }
    }

    /**
     * Method 1: Get plugin version from Settings > Plugins
     */
    private static String getVersionFromPluginSettings(RemoteRobot remoteRobot) {
        try {
            log.debug("Trying to get plugin version from Settings > Plugins...");

            // Open Settings via File menu
            openSettings(remoteRobot);

            // Navigate to Plugins section
            navigateToPluginsSection(remoteRobot);

            // Get version information
            String version = extractPluginVersionFromList(remoteRobot);

            // Close Settings
            closeSettings(remoteRobot);

            return version;

        } catch (Exception e) {
            log.debug("Failed to get plugin version from Settings", e);
            try {
                closeSettings(remoteRobot);
            } catch (Exception closeEx) {
                log.debug("Failed to close Settings after error", closeEx);
            }
            return null;
        }
    }

    /**
     * Open IntelliJ Settings dialog
     */
    private static void openSettings(RemoteRobot remoteRobot) {
        try {
            if(remoteRobot.isMac()) {
                log.info("Opening Settings dialog");
                var settingIcon = remoteRobot.find(CommonContainerFixture.class,
                        byXpath("//div[@accessiblename='IDE and Project Settings']"),
                        SHORT_TIMEOUT);
                settingIcon.click();

                var actionList = remoteRobot.find(
                    JListFixture.class,
                    byXpath("//div[@class='MyList']"),
                    Duration.ofSeconds(3));
    
                actionList.findText("Settings…").click();

                log.info("Opened Settings dialog");
            } else {
                log.info("Opening Settings dialog");
                var mainMenu = remoteRobot.find(CommonContainerFixture.class,
                        byXpath("//div[@tooltiptext='Main Menu']"),
                        SHORT_TIMEOUT);
                mainMenu.click();
                Keyboard keyboard = new Keyboard(remoteRobot);
                keyboard.hotKey(KeyEvent.VK_CONTROL, KeyEvent.VK_ALT, KeyEvent.VK_S);
                TimeUtil.doDelay(1000);
                log.info("Opened Settings dialog");
            }
        } catch (Exception e) {
            log.debug("Failed to open Settings via menu", e);
            throw new RuntimeException("Could not open Settings dialog", e);
        }
    }

    /**
     * Navigate to Plugins section in Settings
     */
    private static void navigateToPluginsSection(RemoteRobot remoteRobot) {
        try {
            log.info("Navigating to Plugins section in Settings");
            var settingTree = remoteRobot.find(JTreeFixture.class, byXpath("//div[@class='MyTree']"));
            // Look for Plugins in the left panel
            settingTree.findText("Plugins").click();
            log.info("Open tabs plugins");

            var installedTab = remoteRobot.find(CommonContainerFixture.class,
                    byXpath("//div[@text='Installed']"),
                    SHORT_TIMEOUT);
            installedTab.click();
            log.info("Click on Installed tab");

            TimeUtil.doDelay(500);

        } catch (Exception e) {
            throw new RuntimeException("Could not navigate to Plugins section", e);
        }
    }

    /**
     * Extract plugin version from plugin list
     */
    private static String extractPluginVersionFromList(RemoteRobot remoteRobot) {
        try {
            log.info("Searching for plugin in installed list...");
            var marketplaceInput = remoteRobot.find(
                    JTextFieldFixture.class,
                    byXpath("//div[@accessiblename='Search plugins']"),
                    SHORT_TIMEOUT);

            marketplaceInput.setText(GEMINI_PLUGIN_NAME);

            var xPathPlugin = "//div[@accessiblename='" + GEMINI_PLUGIN_NAME + "' and @class='ListPluginComponent']";
            remoteRobot.find(
                    CommonContainerFixture.class,
                    byXpath(xPathPlugin),
                    DEFAULT_TIMEOUT);

            log.info("Found plugin component, looking for version info...");

            var versionLabels = remoteRobot.findAll(
                    JLabelFixture.class,
                    byXpath(xPathPlugin + "//div[@class='NonOpaquePanel']//div[@class='JLabel']"));
            log.info("Found {} labels in plugin component", versionLabels.size());
            var version = versionLabels.get(0).getValue();
            log.info("Data: {}", version);

            return version.toString();

        } catch (Exception e) {
            log.debug("Failed to extract plugin version from list", e);
            return null;
        }
    }

    /**
     * Close Settings dialog
     */
    private static void closeSettings(RemoteRobot remoteRobot) {
        try {
            // Try to find and click Cancel/Close button
            try {
                remoteRobot.find(JButtonFixture.class, byXpath("//div[@text='Cancel']"), SHORT_TIMEOUT).click();
            } catch (Exception e) {
                // Try close button
                try {
                    remoteRobot.find(ComponentFixture.class, byXpath("//div[@accessiblename='Close']"),
                            SHORT_TIMEOUT).click();
                } catch (Exception closeEx) {
                    log.debug("Could not find close button, dialog may be closed already", closeEx);
                }
            }
            TimeUtil.doDelay(500);

        } catch (Exception e) {
            log.debug("Failed to close Settings dialog", e);
        }
    }
}