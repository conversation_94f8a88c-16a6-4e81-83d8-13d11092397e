package com.cienet.idetest.util;

import com.cienet.idetest.vo.TestConstant;
import lombok.extern.slf4j.Slf4j;
import com.cienet.idetest.test.ui.BottomBar;
import com.cienet.idetest.test.ui.ChatWindow;
import com.cienet.idetest.test.ui.LeftManuBar;
import com.cienet.idetest.test.ui.OpenProjectMenu;
import com.cienet.idetest.test.ui.PopUpWindow;
import org.openqa.selenium.*;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

import java.io.IOException;
import java.util.List;

@Slf4j
public class UiUtil {

    public static WebElement findElement(WebDriverWait wait, String xpath) {
        return findElement(wait, By.xpath(xpath));
    }

    public static WebElement findElement(WebDriverWait wait, By by) {
        log.debug("findElement - by: {}", by);
        return wait.until(ExpectedConditions.visibilityOfElementLocated(by));
    }

    public static List<WebElement> findElements(WebDriverWait wait, String xpath) {
        return findElements(wait, By.xpath(xpath));
    }

    public static List<WebElement> findElements(WebDriverWait wait, By by) {
        log.debug("findElements - by: {}", by);
        return wait.until(ExpectedConditions.presenceOfAllElementsLocatedBy(by));
    }

    public static WebElement findElementPresence(WebDriverWait wait, By by) {
        log.debug("findElementPresence - by: {}", by);
        return wait.until(ExpectedConditions.presenceOfElementLocated(by));
    }

    public static WebElement findEditorPane(WebDriverWait wait) {
        return findElement(wait,
                By.xpath("//*[contains(@class,'monaco-mouse-cursor-text')] //div[contains(@class, 'view-line')]"));
    }

    public static boolean isElementPresent(WebDriver driver, By locator) {
        List<WebElement> elements = driver.findElements(locator);
        if (elements.isEmpty()) {
            return false;
        }
        for (WebElement element : elements) {
            if (element.isDisplayed()) {
                return true;
            }
        }
        return false;
    }

    public static boolean isElementPresent(WebDriver driver, String xpath) {
        return isElementPresent(driver, By.xpath(xpath));
    }

    public static void checkRestrictedModeAndAccept(WebDriverWait wait) {
        if (CommonUtil.getOsTyppe() == CommonUtil.OsTypeEnum.Windows) {
            return;
        }
        WebElement restrictedModeBar = findElement(wait, By.xpath(
                "//*[text()='Restricted Mode is intended for safe code browsing. Trust this window to enable all features.']"));
        if (restrictedModeBar.isDisplayed()) {
            WebElement manageLink = findElement(wait, By.xpath("//*[text()='Manage']"));
            manageLink.click();

            WebElement trustButton = findElement(wait, By.xpath("//*[text()='Trust']"));
            trustButton.click();
        }
    }

    public static boolean checkTestMode(WebDriverWait wait) {
        By xpath = By.xpath("//*[contains(text(), 'TEST MODE: All metrics will be sent to the test environment')]");
        List<WebElement> elements = findElements(wait, xpath);
        return !elements.isEmpty();
    }

    public static void trustAuthors(WebDriverWait wait) {
        findElement(wait, By.xpath("//a[text()='Yes, I trust the authors']")).click();
    }

    public static WebElement getEditorWindow(WebDriverWait wait) {
        return findElement(wait, By.className("editor-instance"));
    }

    public static void selectLine(Actions actions) {
        actions.sendKeys(Keys.HOME).perform();
        actions.keyDown(Keys.SHIFT).sendKeys(Keys.END).keyUp(Keys.SHIFT).perform();
    }

    public static void goToLineNumber(int lineNumber, Actions actions) {
        for (int i = 1; i < lineNumber; i++) {
            actions.sendKeys(Keys.ARROW_DOWN).perform();
        }
    }

    public static void selectLines(Actions actions, int lineNumFrom, int lineNumTo) {
        for (int i = 1; i < lineNumFrom; i++) {
            actions.sendKeys(Keys.ARROW_DOWN).perform();
        }
        actions.sendKeys(Keys.HOME).perform();
        actions.keyDown(Keys.SHIFT).perform();
        int linesToSelect = lineNumTo - lineNumFrom;
        for (int i = 1; i <= linesToSelect; i++) {
            actions.sendKeys(Keys.ARROW_DOWN).perform();
            TimeUtil.doDelay(150);
        }
        actions.sendKeys(Keys.END).keyUp(Keys.SHIFT).perform();
    }

    public static void sendKeyWithDelay(Actions actions, String prompt, int delay) {
        prompt.chars().forEach(c -> {
            actions.sendKeys(String.valueOf((char) c)).perform();
            TimeUtil.doDelay(delay);
        });
    }

    public static void typeText(Actions actions, String prompt) {
        sendKeyWithDelay(actions, prompt, 150);
    }

    /**
     * Start VSC through ChromeDriver and select google project for Gemini Chat
     *
     * @return
     */
    public static void prepareVscForTest(WebDriver driver, WebDriverWait wait) {
        // checkRestrictedModeAndAccept(wait);

        BottomBar bottomBar = new BottomBar(driver, wait);
        PopUpWindow popUpWindow = new PopUpWindow(driver, wait);
        ChatWindow chatWindow = bottomBar.openChatWindow();
        // chatWindow.stepInto();
        // chatWindow.selectGoogleProjectButton().click();
        // chatWindow.stepOut();
        // popUpWindow.selectGoogleCloudProjectByKeys();
    }

    public static void openResourceProject(WebDriver driver) {
        try {
            // Wait for the dialog to appear
            Thread.sleep(3000);

            // Determine the project path
            String userName = System.getProperty("user.name");
            String projectPath = TestConstant.UITEST_TEST_PLAN_RESOURCE_PATH;
            log.debug("Open {}:", projectPath);

            // Handle File Chooser
            RobotUtil.handleFileChooser(projectPath);

            // Wait for the folder to open
            Thread.sleep(2000);

        } catch (Exception e) {
            throw new RuntimeException("Error while opening resource project", e);
        }
    }

    public static void openNewFile(Actions actions) {
        // Open new file
        Keys ctrlKey = CommonUtil.getCtrlKey();
        actions.keyDown(ctrlKey).sendKeys("n").keyUp(ctrlKey).perform();
    }

    public static void openResourceProjectForTest(WebDriver driver, WebDriverWait wait, String... fileNameToPerformTest)
            throws IOException {
        LeftManuBar leftManuBar = new LeftManuBar(driver, wait);
        OpenProjectMenu openProjectMenu = leftManuBar.clickOpenProjectButton();
        openProjectMenu.openProject();

        TimeUtil.doDelayInSeconds(2);
        openResourceProject(driver);
        TimeUtil.doDelayInSeconds(2);
        trustAuthors(wait);
        CharSequence ctrlKey = CommonUtil.getCtrlKey();

        for (String file : fileNameToPerformTest) {
            Actions actions = new Actions(driver);
            actions.keyDown(ctrlKey).sendKeys("p").keyUp(ctrlKey).perform();

            WebElement quickMenu = findElement(wait,
                    By.cssSelector(
                            "input.input.empty[placeholder='Search files by name (append : to go to line or @ to go to symbol)']"));
            quickMenu.sendKeys(file);
            TimeUtil.doDelayInSeconds(4);
            actions.sendKeys(Keys.ENTER).perform();
            TimeUtil.doDelayInSeconds(2);
        }

        // Wait until Gemini Icon appears.
        WebElement geminiIcon = findElement(wait, "//span[@class = 'codicon codicon-material-spark']");
        log.debug("Found geminiIcon: {}", geminiIcon);

        // Close copilot window
        WebElement copilotWelcome = driver.findElement(By.xpath("//*[contains(text(), 'Welcome to Copilot')]"));
        log.debug("copilotWelcome: {}", copilotWelcome);

        if (copilotWelcome != null) {
            log.debug("Hide copilot window.");
            findElement(wait, "//*[contains(@aria-label, 'Hide Secondary Side Bar')]").click();
        }
    }

    // private static void executeOascript(String scptFilename) throws IOException {
    //     String scptPath = UITEST_OSA_SCRIPT_PATH + "/" + scptFilename;
    //     log.debug("executeOascript: {}, canonical: {}, test-plan-res: {}", scptPath, UITEST_CANONICAL_ANSWER_PATH,
    //             UITEST_TEST_PLAN_RESOURCE_PATH);
    //     Process p = Runtime.getRuntime()
    //             .exec(String.format("osascript %s %s %s", scptPath, UITEST_CANONICAL_ANSWER_PATH,
    //                     UITEST_TEST_PLAN_RESOURCE_PATH));
    //     String stdout = IOUtils.toString(p.getInputStream());
    //     String stderr = IOUtils.toString(p.getErrorStream());
    //     log.debug("osascript process info: stdout={}, stderr={}", stdout, stderr);
    //
    //     assertTrue(stderr == null || stderr.isEmpty(), "Execute osascript failed. stderr: " + stderr);
    // }

    public static void makeSureDiffViewIsEnabled(WebDriver driver, WebDriverWait wait, Actions actions) {
        actions.keyDown(Keys.LEFT_SHIFT).keyDown(Keys.COMMAND).sendKeys("p").keyUp(Keys.LEFT_SHIFT).keyUp(Keys.COMMAND)
                .perform();
        WebElement quickMenu = findElement(wait, By.cssSelector("[aria-describedby='quickInput_message']"));
        quickMenu.sendKeys("Open User Settings (JSON)");
        actions.sendKeys(Keys.ENTER).perform();
        if (!driver.getPageSource().contains("cloudcode.duetAI.codeGenerationPaneViewEnabled")) {
            actions.sendKeys(Keys.END).perform();
            actions.sendKeys("\"cloudcode.duetAI.codeGenerationPaneViewEnabled\": true,").perform();
            actions.keyDown(Keys.COMMAND).sendKeys("s").keyUp(Keys.COMMAND).perform();
            TimeUtil.doDelay(500);
            actions.keyDown(Keys.COMMAND).sendKeys("w").keyUp(Keys.COMMAND).perform();
        }
    }

    public static void openPythonNewFile(Actions actions) {
        // cmd+n to New Text File
        actions.keyDown(Keys.COMMAND).sendKeys("n").keyUp(Keys.COMMAND).perform();
        TimeUtil.doDelay(500);
        // cmd+k (need second key of chord)
        actions.keyDown(Keys.COMMAND).sendKeys("k").keyUp(Keys.COMMAND).perform();
        TimeUtil.doDelay(500);
        // m (second key for Select a model)
        actions.sendKeys("m").perform();
        TimeUtil.doDelay(500);
        actions.sendKeys("python").sendKeys(Keys.ENTER).perform();
        TimeUtil.doDelayInSeconds(2);
    }

    // Scroll to the element to ensure it's in the viewport
    public static void scrollToElement(WebDriver driver, String xpath) {
        String scrollJs = "var el = document.evaluate(arguments[0], document, null, XPathResult.FIRST_ORDERED_NODE_TYPE," +
                " null).singleNodeValue; if (el) el.scrollIntoView(true);";
        JavascriptExecutor js = (JavascriptExecutor) driver;
        log.debug("scrollToElement - js: {}, xpath: {}", scrollJs, xpath);
        js.executeScript(scrollJs, xpath);
    }

    public static WebElement scrollToAndFind(WebDriver driver, WebDriverWait wait, String xpath) {
        scrollToElement(driver, xpath);
        return findElement(wait, xpath);
    }

    public static void clearNotificatons(WebDriver driver) {
        try {
            List<WebElement> buttons = driver.findElements(By.xpath(
                    "//*[contains(@class, 'notification-list-item-toolbar-container')]" +
                            "//a[contains(@class, 'codicon-notifications-clear')]"));
            for (WebElement button : buttons) {
                if (!button.isDisplayed() || !button.isEnabled()) {
                    continue;
                }
                try {
                    button.click();
                } catch (Exception e) {
                    log.debug("got exception in clearNotifications()", e);
                    // ignroed
                }
                TimeUtil.doDelay(500);
            }
        } catch (Exception e) {
            log.debug("Clear notifications failed. e: {}", e);
            // ignored
        }
    }

}
