package com.cienet.idetest.test.inline;

import com.cienet.idetest.test.ui.DiffView;
import com.cienet.idetest.test.ui.TelemetryPanel;
import com.cienet.idetest.util.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.WebElement;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_496_Test extends BaseInlineTest {

    @Test
    public void testInlineGenerate() throws IOException {
        assertTrue(UiUtil.checkTestMode(wait));
        UiUtil.openResourceProjectForTest(driver, wait, "Chat/empty.py");
        TimeUtil.doDelay(1000);
        UiUtil.clearNotificatons(driver);
        TimeUtil.doDelay(1000);
        RobotUtil.clickFullScreenKey();
        // ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView(true);", editorPane);
        // editorPane.click();

        TelemetryPanel telemetry = new TelemetryPanel(driver, wait, actions);
        telemetry.open();
        TimeUtil.doDelay(2000);

        WebElement inlineChat = triggerInlineChat(true);
        inlineChat.sendKeys("/generate a flask app");
        TimeUtil.doDelay(2000);
        RobotUtil.clickEnterKey();
        // actions.keyDown(Keys.ENTER).keyUp(Keys.ENTER); // it doesn't work
        TimeUtil.doDelay(5000);
        DiffView diffView = new DiffView(driver, wait, "empty.py");

        // Find the source code in the left
        WebElement sourcePane = diffView.getSourcePane();
        assertTrue(sourcePane.isDisplayed());

        // Find the suggested source code in the right
        WebElement suggestedPane = diffView.getSuggestedPane();
        assertTrue(suggestedPane.isDisplayed());

        // Find /generate in telemetry log
        // Use filter to move focus to the log text
        telemetry.filterText("\"key\":\"slash_command\",\"value\":\"/generate\"");

        // Maximize the log panel
        telemetry.maximize();

        // Find "slash_command" and "/generate"
        String slashCmd = telemetry.getSlashCommand();
        assertTrue(slashCmd.equals("/generate"), "slash_command is not /generate!");
        TimeUtil.doDelay(2000);
    }

}
