package com.cienet.idetest.test.completion;

import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;

import static com.cienet.idetest.util.UiUtil.findElement;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
@ExtendWith(TestExceptionHandler.class)
public class Gemini_288_Test extends BaseCompletionTest {

    /**
     * Gemini-288 : Completions in untitled and unsaved files- [VSC] - Version1
     * To validate Code Completion function works with untitled and unsaved files.
     *
     * Test compared with TesLink on: 4/10/2024
     */
    @Test
    public void test() {
        // Given
        UiUtil.openPythonNewFile(actions);

        // When
        WebElement editArea = findElement(wait, By.xpath("//*[contains(@class,'editor-scrollable')]"));
        actions.sendKeys("# function to add 2 numbers").sendKeys(Keys.ENTER).perform();
        String prompt = "def add_numbers(num1,";
        prompt.chars().forEach(c -> {
            actions.sendKeys(String.valueOf((char) c)).perform();
            TimeUtil.doDelay(150);
        });
        TimeUtil.doDelayInSeconds(1);

        // Then
        WebElement acceptWord = findElement(wait, By.xpath("//*[contains(text(),'Accept Word')]"));
        log.debug("Final code with completion: {}", editArea.getText());
        assertTrue(acceptWord.getText().contains("Accept Word"), "Cannot find \"Accept Word\" on UI");

        // ChromeUtil.doScreenShot();
    }

}