# VSCode IDE Test Automation

This project focuses on automating UI tests for Visual Studio Code using Selenium. It includes tests for the "Gemini Code Assist" feature, which can be triggered via REST API. Upon starting the application, you can access the Swagger documentation at [http://localhost:8080/swagger-ui/index.html](http://localhost:8080/swagger-ui/index.html).

## Table of Contents

- [Quick Overview](#quick-overview)
- [Technical Stack](#technical-stack)
- [How to Build The Agent](#how-to-build-the-agent)
- [Supported OS and CPU Architecture](#supported-os-and-cpu-architecture)
- [Agent Setup Guide](#agent-setup-guide)
- [Key Components and Interactions](#key-components-and-interactions)

## Quick Overview

```mermaid
flowchart TB
    %% USER INTERFACE LAYER
    subgraph UI["User Interface"]
        UI_1("Swagger UI<br/>CI/CD System<br/>CLI Tools")
    end

    %% APPLICATION LAYER
    subgraph APP["Test Automation Agent"]
        direction TB

        subgraph API["API Layer"]
            direction TB
            API_1("Spring Boot API")
            API_2("Test Run Controller")
        end

        subgraph SVC["Service Layer"]
            direction TB
            SVC_1("Test Runner")
            SVC_2("Error Handler")
            SVC_3("Debug Log Collector")
        end

        subgraph EXE["Execution Layer"]
            direction TB
            subgraph TST_CASE["Test Cases"]
               TST_CASE_1@{ shape: procs, label: "Test Case Implementation, e.g., Gemini_15_Test.java, Gemini_17_Test.java, Gemini_266_Test.java ..."}
            end
            subgraph TST_FRM["Test Frameworks"]
               direction TB
               TST_FRM_1("JUnit 5 Engine")
               TST_FRM_2("Selenium WebDriver")
            end
        end
    end

    %% IDE LAYER
    subgraph IDE["IDE Environment"]
        IDE_1("VS Code<br/>Gemini Code Assist")
    end

    %% FLOW CONNECTIONS
    UI e1@<==>|HTTP Requests / Responses| APP
    API e2@-->|Delegate Operations| SVC
    SVC e3@-->|Execute Tests| EXE
    EXE e4@-->|Results & Logs| SVC
    SVC e5@-->|Test Summary| API
    APP e6@<==>|UI Commands & Results| IDE

    %% STYLING DEFINITIONS
    classDef uiStyle fill:#e5e7eb,stroke:#94a3b8,stroke-width:2px,color:#1f2937,font-weight:bold
    classDef appStyle fill:#f3f4f6,stroke:#cbd5e1,stroke-width:2px,color:#1e293b,font-weight:bold
    classDef apiStyle fill:#f9fafb,stroke:#60a5fa,stroke-width:2px,color:#1e3a8a,font-weight:bold
    classDef svcStyle fill:#f1f5f9,stroke:#38bdf8,stroke-width:2px,color:#0c4a6e,font-weight:bold
    classDef exeStyle fill:#fef3c7,stroke:#facc15,stroke-width:2px,color:#78350f,font-weight:bold
    classDef ideStyle fill:#ecfdf5,stroke:#34d399,stroke-width:2px,color:#064e3b,font-weight:bold

    classDef nodeStyle fill:#ffffff,stroke:#d1d5db,stroke-width:1.5px,color:#1f2937,font-weight:500

    class UI uiStyle
    class APP appStyle
    class API apiStyle
    class SVC apiStyle
    class EXE apiStyle
    class TST_CASE svcStyle
    class TST_FRM svcStyle
    class IDE uiStyle

    class UI_1,API_1,API_2,SVC_1,SVC_2,SVC_3,SVC_4,TST_FRM_1,TST_FRM_2,TST_CASE_1,IDE_1 nodeStyle

    e1@{ animate: true }
    e2@{ animate: true }
    e3@{ animate: true }
    e4@{ animate: true }
    e5@{ animate: true }
    e6@{ animate: true }
```

### For More System Design Diagrams

- [Flowchart Diagram](./docs/flowchart-diagram.md)

## Technical Stack

### Core Framework

- **Spring Boot**: 3.2.5
- **Java**: JDK 21
- **Build Tool**: Gradle 8.7

### Testing Framework

- **Selenium Java 4.28.1**
- **WebDriverManager 5.7.0**
- **JUnit 5**

## How to Build The Agent

Follow these steps to build VSCode Test Automation Agent:

1. **Clone Repositories**:

The `ide-test-automation/vscode` repository contains the main Java source code, while `test-plan-resource` stores sample code used in automated tests.

```bash
    git clone test-plan-resource
    git clone https://github.com/CIeNET-International/ide-test-automation
```
1. **Build The Agent**:

   The following command builds the agent product image `vsc.agent.prod.tar.gz`, which includes the binary and sample code for testing.

   ```bash
       cd ide-test-automation/vscode
       ./gradlew clean prodTar
   ```

The file `vsc.agent.prod.tar.gz` will be in `build/tar` directory.

## Supported OS and CPU Architecture

VSCode test automation agents can run on various operating systems with x86-64 or ARM64 CPUs, provided the correct version of ChromeDriver is installed on the device. Supported operating systems include macOS, Windows 11, and Linux.

## Agent Setup Guide

> [!IMPORTANT]
>
> - Currently, the automation agent assumes that `Gemini Code Assist` is the only extension appearing in the context menu on the left-hand side of VSCode. While enhancements are underway to remove this limitation, please uninstall any unrelated extensions for now to ensure the automation runs smoothly.
> - Please avoid moving any opened applications, windows, or the cursor position during UI automation to ensure the interactions work correctly.

The following steps describe how to set up the automated agent on the devices you want to test.

1. **Extract vsc.agent.prod.tar.gz**

   Create a testhome directory under your home directory, and extract the contents of `vsc.agent.prod.tar.gz` into it. The archive includes several files and directories, including the startup scripts:

   - `startagent.sh` for macOS and Linux
   - `startagent.ps1` for Windows 11

   ```bash
    mkdir ~/testhome
    cd ~/testhome
    tar zxvf vsc.agent.prod.tar.gz
   ```

1. **Install Java 21**

   Download OpenJDK 21 and install it in your testing device.

1. **Download ChromeDriver**

   ChromeDriver is needed for running VSCode Automated Test Agent, and it can be downloaded in [GitHub Electron repository](https://github.com/electron/electron/releases).
   Check the Electron version used by VSCode in the “About” page, and download the ChromeDriver that matches that specific Electron version.
   For example, VSCode version 1.101.0 is based on Electron 35.5.1, so you should download the ChromeDriver from:
   [https://github.com/electron/electron/releases/tag/v35.5.1](https://github.com/electron/electron/releases/tag/v35.5.1)

   Edit the startup script `startagent.sh` (or `startagent.ps1` in Windows 11) and set the path to the ChromeDriver using the system property `webdriver.chrome.driver`.

   For example:

   ```bash
   -Dwebdriver.chrome.driver=$HOME/testhome/chromedriver-mac-arm64/chromedriver
   ```

1. **_VSCode and Gemini Code Assist_**

   Gemini Code Assist must be installed in VSCode. Use the Google account you intend to run the tests with to sign in to Gemini Code Assist.
   In the startup script `startagent.sh`, specify the project ID using the system property `uitest.google.project.id`.

   For example:

   ```bash
   -Duitest.google.project.id=ai-dev-preview-external
   ```

1. **Enable GCA Test Mode**

   Test mode must be enabled to run test cases. Set the appropriate environment variable before starting the VSCode Automated Test Agent.

   - **_MacOS:_**
     Add `export CLOUDCODE_METRICS_MODE=TEST` in `.zshrc` (or `.bash_profile` for bash shell)

   - **_Linux:_**
     Add `export CLOUDCODE_METRICS_MODE=TEST` in `.bashrc`

   - **_Windows:_**
     Add `CLOUDCODE_METRICS_MODE=TEST` to System Properties > Advanced > `Environment Variables`

1. **Run the VSCode Automated Test Agent**

   - **_Start the Rest API Server_**

     In macOS and Linux:

     ```bash
     ./startagent.sh
     ```

     In Windows 11:

     ```bash
     .\startagent.ps1
     ```

     A Spring Boot based Rest API server will be started and listening to the request.

   - **_Grant proper permissions_**

     - In macOS, you need to grant permissions to the automation agent (via Terminal) to control your computer.

     - Specifically, go to `Privacy & Security` menu in System Settings, grant the `Accessibility` and `Screen & System Audio Recording` to your Terminal application (this could be the built-in Terminal in MacOS, iTerms, or any Terminal app you use.)

     - During UI automation, you might also need to grant permission to `chromedriver` and `libffmpeg.dylib` from the `Security` section in the `Privacy & Security` menu

   - **Run Test Cases**

     - **_Swagger UI_**

       Once the agent has started, you can access its Swagger UI to view the available REST APIs and execute test cases.
       URL:

       http://${agentHostIp}:8080/swagger-ui/index.html

     - **_Command Line Script_**

       You can also run test cases using the Bash script [runtest.sh](https://github.com/CIeNET-International/ide-test-automation/blob/main/shared/src/main/resources/scripts/runtest.sh) from the _ide-test-automation/shared_ repository.

       The syntax is

       ```bash
       runtest.sh -s <agentHostIp> -n <testCaseName>
       ```

       For example:

       ```bash
       runtest.sh -s ************* -t Gemini_17,Gemini_6,Gemini_496 -o api_output
       ```

1. **Logs and Trouble Shooting**

   The agent’s log files are written to the ~/testhome/logs directory.

1. **Test Video Recording**

   Video Recording of test cases is enabled by default. You can disable it by setting `-Duitest.enable.screen.record=false`. The number of video files of a test case can be set by `-Duitest.max.video.files=${numOfFiles}.` It's 5 by default.
   To enable video recording you have grant the permission of video recording to terminal app in MacOS and Ubuntu. The recorded video files are in the ~/testhome/video directory.

## Key Components and Interactions

### Dataflow Quick Summary

1. A user request hits the `TestRunController`.

1. The controller, after managing concurrency, delegates to `TestRunnerService` for test execution.

1. `TestRunnerService` uses JUnit to run actual test files (e.g., `Gemini_105_Test.java`)

1. These test classes use `Selenium WebDriver` (via ChromeUtil.getChromeDriver()) to drive the `VS Code UI` and interact with the `Gemini Code Assist Plugin`.

1. Results are collected by `TestRunnerService` and returned via the `TestRunController` to the user.

### Explaination

1.  **API Layer**

    - **[TestRunController.java](src/main/java/com/cienet/idetest/web/TestRunController.java)**
      - Provides entry point for all API requests (e.g., `/tests/run/{testName}`), handles the incoming requests.
      - Manages concurrency with a ReentrantLock (ensuring sequential test execution), and delegates logic to the Service Layer (e.g., `TestRunnerService`).

1.  **Service Layer**

    - **[TestRunnerService.java](src/main/java/com/cienet/idetest/service/TestRunnerService.java)**
      - It's responsible for discovering JUnit tests, executing them programmatically, and compiling results (including logs and screenshots) into the `TestSummary` objects.

1.  **Test Execution Layer**

    - **JUnit 5 Engine**
      - The core JUnit framework that executes the test methods.
    - **[BaseTest.java](src/main/java/com/cienet/idetest/test/BaseTest.java)**
      - Foundation for all test classes. Manages WebDriver lifecycle, screen recording, and initial VS Code setup for each test.
      - Provides common interaction with the Selenium WebDriver and UI actions.
    - **Individual Test Classes (e.g., [Gemini_105_Test.java](src/main/java/com/cienet/idetest/test/generation/Gemini_105_Test.java), [Gemini_266_Test.java](src/main/java/com/cienet/idetest/test/chat/Gemini_266_Test.java))**
      - Contains the actual test steps and assertions, using methods provided by BaseTest and Page Object classes.
      - Interacts with VS Code UI elements using Selenium WebDriver commands, often abstracted via Page Object classes in the test/ui/ package (e.g., typing in the editor, clicking buttons in the chat panel, verifying text).
