package org.cienet.ide_intellij_automation_test.test.codeCompletion;

import org.cienet.ide_intellij_automation_test.util.TestExceptionHandler;

import com.cienet.ide.test.common.constants.FileContentIde;
import com.cienet.ide.test.common.constants.FileNameTest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.IOException;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_17_Test extends BaseCompletionTest {

    /**
     * Gemini-17
     * Basic sanity test that code completion can be triggered with Python.
     * Only tests if code completion can be triggered and returns a suggestion -
     * does not test the syntax, formatting, or quality of the suggestion.
     *
     * @throws IOException
     */
    @Test
    public void Gemini_17() throws IOException {
        doSimpleCompletionTest(FileNameTest.SORT_ARRAY_PYTHON, FileContentIde.SORT_ARRAY_PYTHON);
    }

}