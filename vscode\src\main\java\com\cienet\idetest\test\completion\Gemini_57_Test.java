package com.cienet.idetest.test.completion;

import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.ide.test.common.vo.LanguageTypeEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.IOException;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_57_Test extends BaseCompletionTest {

    /**
     * Gemini-57
     * Basic sanity test that code completion can be triggered with Java.
     * Only tests if code completion can be triggered and returns a suggestion -
     * does not test the syntax, formatting, or quality of the suggestion.
     *
     * @throws IOException
     */
    @Test
    public void testJavaCodeComplete() throws IOException {
        String filePath = "Code Completion/Java/SortArray.java";
        String comment = "// Given an array of integers nums, sort the array in ascending order using quick sort and return it";
        String funcDeclare = "public int[] sortArray(int[] nums) {";
        doCompletionTest(filePath, comment, funcDeclare);
    }

}