package org.cienet.ide_intellij_automation_test.service;

import com.redhat.devtools.intellij.commonuitest.AbstractLibraryBaseTest;
import com.redhat.devtools.intellij.commonuitest.UITestRunner;
import com.redhat.devtools.intellij.commonuitest.fixtures.dialogs.FlatWelcomeFrame;
import com.redhat.devtools.intellij.commonuitest.fixtures.dialogs.settings.SettingsDialog;
import com.cienet.ide.test.common.constants.DelayTimeInt;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.intellij.remoterobot.RemoteRobot;
import com.intellij.remoterobot.fixtures.CommonContainerFixture;
import com.intellij.remoterobot.fixtures.JCheckboxFixture;
import com.intellij.remoterobot.fixtures.JTextFieldFixture;
import com.intellij.remoterobot.fixtures.JTreeFixture;
import com.intellij.remoterobot.utils.Keyboard;
import com.redhat.devtools.intellij.commonuitest.utils.constants.XPathDefinitions;
import com.redhat.devtools.intellij.commonuitest.utils.runner.IntelliJVersion;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;

import java.util.logging.Logger;
import java.io.FileInputStream;
import java.time.Duration;
import java.util.Properties;
import java.awt.event.KeyEvent;
import org.cienet.ide_intellij_automation_test.util.TimeUtil;
import static com.intellij.remoterobot.search.locators.Locators.byXpath;

@Slf4j
@Service
public class IntellijManagerService {
  private static IntelliJVersion communityIdeaVersion = AbstractLibraryBaseTest.loadIntellijVersion();

  private final Logger LOGGER = Logger.getLogger(IntellijManagerService.class.getName());
  public RemoteRobot remoteRobot;
  
  @Value("${project.path}")
  private String projectPath;

  @Value("${port.runner}")
  private static String portRunner;


  @PostConstruct
  public void init() {
    loadIntellijVersion();
    System.out.println("Initializing IntellijManager with platform version: " + ideaVersion());
  }
  int port = portRunner != null ? Integer.parseInt(portRunner) : 8580;

  private String getActivationCode() {
    try {
      Properties props = new Properties();
      props.load(new FileInputStream("activation-code.properties"));
      String code = props.getProperty("activation.code");
      if (code != null) {
        LOGGER.info("Loaded activation code from activation-code.properties");
        return code;
      } else {
        LOGGER.warning("activation.code not found in activation-code.properties, using default");
        return "";
      }
    } catch (Exception e) {
      LOGGER.warning("Failed to read activation code from activation-code.properties: " + e.getMessage() + ", using default");
      return "";
    }
  }

  @PreDestroy
  public void cleanup() {
    closeIntelliJ();
  }

  private void loadIntellijVersion() {
    try {
      String version = System.getProperty("ideaVersion");
      if (version != null) {
        communityIdeaVersion = IntelliJVersion.getFromStringVersion(version);
        LOGGER.info("Loaded IntelliJ version : " + version);
      } else {
        throw new RuntimeException("ideaVersion not found in gradle.properties");
      }
    } catch (Exception e) {
      LOGGER.severe("Failed to read IntelliJ version : " + e.getMessage());
      throw new RuntimeException(e);
    }
  }

  public boolean startIntelliJ() {
    if (!isStarted()) {
      LOGGER.info("Starting IntelliJ IDEA...");
      remoteRobot = UITestRunner.runIde(communityIdeaVersion, port);

      Runtime.getRuntime().addShutdownHook(new CloseIntelliJBeforeQuit());
      return true;
      // FlatWelcomeFrame flatWelcomeFrame = remoteRobot.find(FlatWelcomeFrame.class,
      // Duration.ofSeconds(10));
      // flatWelcomeFrame.clearWorkspace();
      // flatWelcomeFrame.disableNotifications();
    }
    return false;
  }

  public void activateIntelliJ() {
    var activationCodePath = "//div[@text='Activation code']";
    if(elementExists(activationCodePath, DelayTimeInt.DELAY_5)) {
      var activationCode = remoteRobot.find(CommonContainerFixture.class,
          byXpath(activationCodePath),
          Duration.ofSeconds(10));
      activationCode.click();

      var activationCodeInput = remoteRobot.find(JTextFieldFixture.class,
          byXpath("//div[@class='G']"),
          Duration.ofSeconds(10));
      activationCodeInput.setText(getActivationCode());

      var activationCodeButton = remoteRobot.find(CommonContainerFixture.class,
          byXpath("//div[@class='a' and @text='Activate']"),
          Duration.ofSeconds(10));
      activationCodeButton.click();

      var closeButton = remoteRobot.find(CommonContainerFixture.class,
          byXpath("//div[@text='Close']"),
          Duration.ofSeconds(10));
      closeButton.click();
    }
  }

  public boolean isStarted() {
    return UITestRunner.isIntelliJUIVisible(port) && remoteRobot != null;
  }

  public String ideaVersion() {
    return communityIdeaVersion.toString();
  }

  public void closeIntelliJ() {
    UITestRunner.closeIde();

    // if (intelliJHasStarted) {
    // LOGGER.info("Closing IntelliJ IDEA...");
    // intelliJHasStarted = false;
    // }
  }

  private static class CloseIntelliJBeforeQuit extends Thread {
    @Override
    public void run() {
      UITestRunner.closeIde();
    }
  }

  public void testDisableReopenProjectOnstartup() {
    log.info("Running disable reopen project on startup");

    // try {
    // MainIdeWindow mainIdeWindow = remoteRobot.find(MainIdeWindow.class,
    // Duration.ofSeconds(10));
    // mainIdeWindow.closeProject();
    // } catch (Exception e) {
    // log.info("MainIdeWindow not found");
    // }

    FlatWelcomeFrame welcomeFrame = remoteRobot.find(FlatWelcomeFrame.class,
        Duration.ofSeconds(30));
    log.info("IntelliJ welcome frame found");
    // welcomeFrame.clearWorkspace();
    welcomeFrame.openSettingsDialog();
    log.info("Open settings dialog");

    SettingsDialog settingsDialog = remoteRobot.find(SettingsDialog.class,
        Duration.ofSeconds(5));
    settingsDialog.navigateTo("System Settings");
    log.info("Navigate to System Settings");

    JCheckboxFixture reopenCheckbox = remoteRobot.find(
        JCheckboxFixture.class,
        byXpath(
            "//div[@text='Reopen projects on startup']"),
        Duration.ofSeconds(10));
    if (reopenCheckbox.isSelected()) {
      reopenCheckbox.click();
      log.info("Reopen projects on startup is selected, clicking it");
    } else {
      log.info("Reopen projects on startup is already not selected");
    }

    settingsDialog.ok();
    log.info("Successfully disabled reopen project on startup");
  }

  public void installGCA() {
    log.info("Running test to install GCA plugin");

    JTreeFixture jTreeFixture = remoteRobot.find(JTreeFixture.class,
        byXpath(XPathDefinitions.TREE));
    jTreeFixture.findText("Plugins").click();
    log.info("Open tabs plugins");

    var marketplace = remoteRobot.find(CommonContainerFixture.class,
        byXpath("//div[@text='Marketplace']"),
        Duration.ofSeconds(10));
    marketplace.click();
    log.info("Click on Marketplace");

    // Click on the Plugins button in the navigation bar
    JTextFieldFixture marketplaceInput = remoteRobot.find(
        JTextFieldFixture.class,
        byXpath("//div[@class='TextFieldWithProcessing']"),
        Duration.ofSeconds(10));
    var pluginName = "Gemini Code Assist";
    marketplaceInput.setText(pluginName);
    log.info("Clicked search plugin text field");

    String installedCheckBoxPath = "//div[@accessiblename='" + pluginName + "' and @class='ListPluginComponent']//div[@class='JCheckBox']";
    if (elementExists(installedCheckBoxPath, DelayTimeInt.DELAY_5)) {
      remoteRobot.find( CommonContainerFixture.class, byXpath(installedCheckBoxPath)).click();
      return;
    }

    String installButtonXpath = "//div[@accessiblename='" + pluginName + "' and @class='ListPluginComponent']//div[@class='InstallButton']";
    if (elementExists(installButtonXpath, DelayTimeInt.DELAY_10)) {
      CommonContainerFixture geminiPlugin = remoteRobot.find(
          CommonContainerFixture.class,
          byXpath(installButtonXpath),
          Duration.ofSeconds(10));
      geminiPlugin.click();

      String acceptButtonXpath = "//div[@text='Accept']";
      if (elementExists(acceptButtonXpath, DelayTimeInt.DELAY_10)) {
        CommonContainerFixture AcceptButton = remoteRobot.find(
            CommonContainerFixture.class,
            byXpath(
                "//div[@text='Accept']"),
            Duration.ofSeconds(10));
        AcceptButton.click();
      }

      var restartButtonPath = "//div[@accessiblename='" + pluginName + "' and @class='ListPluginComponent']//div[@class='RestartButton']";

      if(elementExists(restartButtonPath, DelayTimeInt.DELAY_120)) {
        log.info("Clicked install button " + pluginName);
        log.info("Successfully installed plugin to plugins marketplace");
      }

      try {
        log.info("Close current window");
        closeCurrentWindow();
        TimeUtil.doDelayInSeconds(10);
        startIntelliJ();
      } catch (Exception e) {
        log.info("Restart button not found, will restart manually");
      }
    }
  }

  public void openProjectTest() {
    remoteRobot.find(FlatWelcomeFrame.class,
      Duration.ofSeconds(10));
    var filePath = "//div[@text='File']";
    var openPath = "//div[@text='File']//div[@text='Open…']";

    if(elementExists(filePath, DelayTimeInt.DELAY_5)) {
      var fileButton = remoteRobot.find(CommonContainerFixture.class,
          byXpath(filePath),
          Duration.ofSeconds(5));
      fileButton.click();

      if(elementExists(openPath, DelayTimeInt.DELAY_10)) {
        var openProjectButton = remoteRobot.find(CommonContainerFixture.class,
            byXpath(openPath),
            Duration.ofSeconds(10));
        openProjectButton.click();

        JTextFieldFixture openProjectInput = remoteRobot.find(
          JTextFieldFixture.class,
          byXpath("//div[@class='BorderlessTextField']"),
          Duration.ofSeconds(5));
        openProjectInput.setText(projectPath);

        remoteRobot.find(CommonContainerFixture.class, byXpath("//div[@text='OK']")).click();
      }
    }
  }

  public void closeCurrentWindow() {
    try {
      if(remoteRobot.isMac() || remoteRobot.isLinux()) {
        closeIntelliJ();
      } else {
        // Try different ways to find and click close button
        new Keyboard(remoteRobot).hotKey(KeyEvent.VK_ALT, KeyEvent.VK_F4);
        LOGGER.info("Attempted to close window using Alt+F4 after waiting 10 seconds");
      }
    } catch (Exception e) {
      LOGGER.warning("Failed to close window: " + e.getMessage());
    }
  }

  public boolean elementExists(String xpath, int timeoutInSeconds) {
    try {
      remoteRobot.find(
          CommonContainerFixture.class,
          byXpath(xpath),
          Duration.ofSeconds(timeoutInSeconds));
      return true;
    } catch (Exception e) {
      LOGGER.info("Element with xpath '" + xpath + "' not found");
      return false;
    }
  }
}