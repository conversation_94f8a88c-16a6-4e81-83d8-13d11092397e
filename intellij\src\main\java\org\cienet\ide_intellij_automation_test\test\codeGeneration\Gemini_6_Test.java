package org.cienet.ide_intellij_automation_test.test.codeGeneration;

import com.intellij.remoterobot.RemoteRobot;

import org.cienet.ide_intellij_automation_test.util.CommonUtil;
import org.cienet.ide_intellij_automation_test.util.TestExceptionHandler;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import com.intellij.remoterobot.fixtures.EditorFixture;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertTrue;

import com.intellij.remoterobot.utils.Keyboard;
import java.awt.event.KeyEvent;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_6_Test extends BaseGenerationTest {

    @Test
    void Gemini_6() throws IOException {
        String filePath = "Code Generation/Python/is_even.py";
        openMultiplesFiles(filePath);
        String prompt = """
                # write a function that checks if input is an even number
                def is_even
                """;

        EditorFixture editor = CommonUtil.prepareEditor(remoteRobot);

        Keyboard keyboard = new Keyboard(remoteRobot);

        CommonUtil.enterTextByLines(keyboard, prompt);

        int initialTextLength = editor.getText().length();

        handleCodeGeneration(remoteRobot, keyboard, editor);

        String generatedText = editor.getText();
        int generatedTextLength = generatedText.trim().length();

        assertTrue(
                generatedTextLength > initialTextLength,
                String.format("Code generation failed. Generated text:\n%s\n", generatedText));
    }

    private void handleCodeGeneration(RemoteRobot remoteRobot, Keyboard keyboard, EditorFixture editor) {
        CommonUtil.triggerCodeGeneration(remoteRobot, keyboard, 30);
        keyboard.key(KeyEvent.VK_TAB);
        editor.click();
    }

}
