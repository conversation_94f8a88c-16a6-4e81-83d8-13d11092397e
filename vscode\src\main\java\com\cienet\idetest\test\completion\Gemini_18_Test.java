package com.cienet.idetest.test.completion;

import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;

import java.io.IOException;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_18_Test extends BaseCompletionTest {

    /**
     * Gemini-18
     * Basic sanity test that code completion can be triggered with Python.
     * Only tests if code completion can be triggered and returns a suggestion
     * which is accepted by TAB -
     * does not test the syntax, formatting, or quality of the suggestion.
     *
     * @throws IOException
     */

    @Test
    public void testCodeComplete() throws IOException {
        String filePath = "Code Completion/Python/empty.py";
        String comment = "# write a function produce a Fi<PERSON><PERSON><PERSON> series";
        String declare = "def fib(n):";
        doCompletionTest(filePath, comment, declare);
    }

}
