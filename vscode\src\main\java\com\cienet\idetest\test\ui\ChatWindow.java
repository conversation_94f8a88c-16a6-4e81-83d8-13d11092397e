package com.cienet.idetest.test.ui;

import com.cienet.idetest.util.TimeUtil;
import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.util.WaitUtil;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.TimeoutException;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.support.ui.WebDriverWait;

import java.time.Duration;
import java.util.List;

import static java.lang.Thread.sleep;
import static com.cienet.idetest.util.UiUtil.findElement;

@Slf4j
public class ChatWindow {

    public static final String XPATH_FILE_CONTAINER_FOLDERS = "//div[contains(@class, 'file-container-header') and contains(text(), '@Folders')]";
    private final WebDriver driver;

    private final WebDriverWait wait;

    public ChatWindow(WebDriver driver, WebDriverWait wait) {
        this.driver = driver;
        this.wait = wait;
    }

    public void stepInto() {
        log.debug("enter stepInto()...Verifying Extensions has been activated...");
        WaitUtil.waitUntilElementIsGone(driver, By.xpath("//*[contains(text(),'Activating Extensions...')]"), 30);
        TimeUtil.doDelayInSeconds(3); // delay 3 secs to start switching iframe

        switchIframeWithRetry(By.cssSelector("iframe[src$='webviewView']"));
        switchIframeWithRetry(By.cssSelector("iframe[title='Chat']"));
    }

    private void switchIframeWithRetry(By by) {
        switchIframeWithRetry(by, 3);
    }

    private void switchIframeWithRetry(By by, int maxRetry) {
        log.debug("do switchIframeWithRetry()...by={}", by);
        for (int i = 1; i <= maxRetry; i++) {
            TimeUtil.doDelayInSeconds(3); // delay 3 secs to do next retry. Don't remove this delay
            WebElement iframe = findElement(wait, by);
            driver.switchTo().frame(iframe);
            if(driver.findElements(by).isEmpty()) {
                break;  // already switch to iframe
            }
            log.debug("Attempt switchIframeWithRetry() with retry: {}", i);
            if (i == maxRetry) {
                log.warn("Fail to switchTo iframe with by={}", by);
            }
        }
    }

//    public void stepInto() {
//        List<WebElement> iframes = wait.until(ExpectedConditions.presenceOfAllElementsLocatedBy(By.cssSelector("iframe[src$='webviewView']")));
//        WebElement iframe = null;
//
//        for (WebElement frame : iframes) {
//            if (frame.isDisplayed()) {
//                iframe = frame;
//                break;
//            }
//        }
//
//        driver.switchTo().frame(iframe);
//        try {
//            sleep(2000);
//            WebElement innerFrame = findElement(wait, By.cssSelector("iframe[title='Chat']"));
//            driver.switchTo().frame(innerFrame);
//            sleep(2000);
//        } catch (InterruptedException e) {
//            throw new RuntimeException(e);
//        }
//    }

    public WebElement selectGoogleProjectButton() {
        TimeUtil.doDelayInSeconds(3);
        log.debug("Find Select Google Project Button");
        return findElement(wait, By.xpath("//*[contains(text(), 'Select a Google Cloud Project')]"));
    }

    public void stepOut() {
        driver.switchTo().defaultContent();
    }

    public WebElement getChatHelloMessage() {
        return findElement(wait, By.xpath("//*[text()='How can Gemini help?']"));
    }

    public WebElement getChatInputField() {
        TimeUtil.doDelay(2000);
        // Use Javascript to make it work on Mac x86.
        String xpath = "//div[contains(@class, 'chat-submit-input')]";
        log.debug("getChatInputField() enter...elements by xpath={}", driver.findElements(By.xpath(xpath)).size());
        UiUtil.scrollToElement(driver, xpath);
        waitForContentEditableToBeVisible(driver, xpath, Duration.ofSeconds(30));
        return UiUtil.findElement(wait, xpath);
    }

    public void waitForContentEditableToBeVisible(WebDriver driver, String xpath, Duration timeout) {
        JavascriptExecutor js = (JavascriptExecutor) driver;
        long endTime = System.currentTimeMillis() + timeout.toMillis();

        while (System.currentTimeMillis() < endTime) {
            Boolean isDisplayed = (Boolean) js.executeScript(
                    "var el = document.evaluate(arguments[0], document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;" +
                            "return !!(el && el.offsetWidth > 0 && el.offsetHeight > 0);",
                    xpath
            );

            log.trace("JS element isDisplayed={}", isDisplayed);

            if (Boolean.TRUE.equals(isDisplayed)) {
                return;
            }

            TimeUtil.doDelay(1000);
        }

        throw new TimeoutException("Element not visible: " + xpath);
    }

    public WebElement getSubmitButton() {
        return findElement(wait, By.cssSelector("button[type='submit']"));
    }

    @Deprecated
    public WebElement _waitUntilChatFinish() {
        WaitUtil.waitUntilElementIsGone(driver, By.xpath("//*[contains(text(),'Gemini Code Assist is working')]"), 30);
        // Wait for rating buttons to appear
        String feedBackXpath = "//div[contains(@class, 'feedback-buttons')]";
        for (int i = 0; i < 3; i++) {
            try {
                log.debug("Attempt {}: Checking visibility of element with XPath: {}", i + 1, feedBackXpath);
                // Scroll to the element to ensure it's in the viewport
                WebElement feekBackEle = UiUtil.scrollToAndFind(driver, wait, feedBackXpath);
                break;
            } catch (Exception e) {
                log.info("Failed to find feekback buttons! i={}", i);
            }
        }
        String chatHistoryXpath = "//div[contains(@class, 'chat-history-item') and contains(@class, 'system')]";
        return UiUtil.scrollToAndFind(driver, wait, chatHistoryXpath);
    }

    public WebElement waitUntilChatFinish() {
        // wait to make sure chat request starts
        TimeUtil.doDelayInSeconds(3);

        // The 'Gemini Code Assist is working' msg no more found since 2.37.0
        // Wait for submit button appears
        int maxTryTimes = 3;
        for (int i = 0; i < maxTryTimes; i++) {
            try {
                log.debug("Attempt getSubmitButton(): {}", i + 1);
                // WebElement submitIcon = findElement(wait, By.xpath("//mat-icon[contains(@class, 'submit-button-send')]"));
                WebElement submitButton = getSubmitButton();
                break;
            } catch (Exception e) {
                log.info("Failed to find submit button! i={}", i);
                if (i >= maxTryTimes) {
                    WebElement submitButton = getSubmitButton(); // final try
                }
            }
        }
        log.debug("submit button found, the chat response should be completed");
        String moveDownXpath = "//*[contains(@class, 'mat-icon notranslate material-icons') "
                + "and contains (text(), 'more_vert')] ";
        WebElement moveDownIcon = UiUtil.findElement(wait, moveDownXpath);
        moveDownIcon.click();
        String chatHistoryXpath = "//div[contains(@class, 'chat-history-item') and contains(@class, 'system')]";
        return UiUtil.scrollToAndFind(driver, wait, chatHistoryXpath);
    }

    public List<WebElement> waitUntilChatFinishAndGetHistory() {
        findElement(wait, By.cssSelector(".chat-history-item.system"));
        return driver.findElements(By.className("history-item-text"));
    }

    public List<WebElement> findChatMsgBox() {
        return driver.findElements(By.xpath("//chat-history-item"));
    }

    public WebElement clearChatButton() {
        return findElement(wait, By.xpath("//ul[@aria-label='Gemini Code Assist: Chat actions']//a[@aria-label='Reset Chat']"));
    }

    public WebElement explainThisButton() {
        return findElement(wait, By.xpath("//*[contains(text(), 'Explain this')]"));
    }

    public void checkIfProjectIsElectedAndSelectIfNot() {
        try {
            sleep(1000);
            WebElement selectProjectButton = selectGoogleProjectButton();
            if (selectProjectButton.isDisplayed()) {
                selectProjectButton.click();
                PopUpWindow popUpWindow = new PopUpWindow(driver, wait);
                selectProjectButton.click();
                stepOut();
                popUpWindow.selectGoogleCloudProjectByKeys();
                stepInto();
            }
        } catch (TimeoutException timeoutException) {
            // Do nothing it is ok that there is no select project button
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    public void closeTipsIfVisible() {
        try {
            WebElement closeButton = findElement(wait, By.xpath("//div[contains(@class, 'chat-header')]//button[.//mat-icon[text()='close']]"));
            if (closeButton.isDisplayed()) {
                closeButton.click();
            }
        } catch (TimeoutException timeoutException) {
            log.info("Timed out while closing tips.");
            // Do nothing it is ok that there is no tips to close
        }
    }

    public void hoverOnChatAndScrollDown(Actions actions) {
        JavascriptExecutor js = (JavascriptExecutor) driver;
        WebElement tips = findElement(wait,By.cssSelector("[title='Tips for getting started']"));
        actions.moveToElement(tips).perform();
        js.executeScript("window.scrollBy(0, 500);");
        TimeUtil.doDelayInSeconds(2);
    }

    public List<WebElement> getFileNameList() {
        return UiUtil.findElements(wait, "//span[@class = 'item-filename']");
    }

    public WebElement getContextItemsButton() {
        return findElement(wait, "//span[contains(@class, 'context-chip-text')]");
    }

    public WebElement getFileItem(String fileName) {
        return findElement(wait, "//span[@class='item-filename' and contains(text(), '" + fileName + "')]");
    }

    public WebElement findFileInMsgBox(String fileName) {
        return findElement(wait, "//span[contains(@class, 'collapse-filename') and contains(text(), '"
                + fileName + "')]");
    }

    public WebElement getContextItem(String fileName) {
        return findElement(wait, "//span[contains(@class, 'entity-name') and contains(text(), '" + fileName + "')]");
    }

    public List<WebElement> getContainerList(boolean waitForElements) {
        String xpath = "//div[contains(@class, 'file-container-header')]";
        List<WebElement> elements;
        if (waitForElements) {
            elements = UiUtil.findElements(wait, xpath);
        } else {
            elements = driver.findElements(By.xpath(xpath));
        }
        return elements;
    }

    public WebElement getFolderDownIcon() {
        return UiUtil.findElement(wait, XPATH_FILE_CONTAINER_FOLDERS
                + "//..//i[contains(@class, 'codicon-chevron-right')]");
    }

    public WebElement getFolderUpIcon() {
        return UiUtil.findElement(wait, XPATH_FILE_CONTAINER_FOLDERS
                + "//..//i[contains(@class, 'codicon-chevron-left')]");
    }

    public WebElement getFolderItem(String folderName) {
        return UiUtil.findElement(wait, XPATH_FILE_CONTAINER_FOLDERS
                + "//..//..//..//span[@class='item-filename' and text()='" + folderName + "']");
    }
}
