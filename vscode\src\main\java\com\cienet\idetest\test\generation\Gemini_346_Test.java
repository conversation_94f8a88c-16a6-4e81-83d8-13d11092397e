package com.cienet.idetest.test.generation;

import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.util.TestExceptionHandler;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;

import java.io.IOException;
import java.util.List;

import static com.cienet.idetest.util.UiUtil.findElement;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_346_Test extends BaseGenerationTest {

    @Test
    void test() throws IOException {
        // Given
        UiUtil.makeSureDiffViewIsEnabled(driver, wait, actions);
        UiUtil.openResourceProjectForTest(driver, wait, "Chat/empty.py");
        clearEditorText();

        // When
        actions.sendKeys("// write a function that checks if input is an even number").perform();
        actions.sendKeys(Keys.ENTER).perform();
        actions.keyDown(Keys.CONTROL).keyDown(Keys.ENTER).perform();
        WebElement iframe = findElement(wait, By.cssSelector("iframe.webview.ready"));
        driver.switchTo().frame(iframe);
        WebElement innerFrame = findElement(wait, By.cssSelector("iframe[title='Gemini Code Assist Suggestions Panel']"));
        driver.switchTo().frame(innerFrame);
        WebElement declineButton = findElement(wait, By.xpath("//a[text()='Decline all']"));
        declineButton.click();
        driver.switchTo().defaultContent();
        List<WebElement> codeLines = driver.findElements(By.className("view-line"));

        // Then
        assertTrue(codeLines.size() <= 2);
    }

}
