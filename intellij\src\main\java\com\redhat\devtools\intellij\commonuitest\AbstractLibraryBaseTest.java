/*******************************************************************************
 * Copyright (c) 2021 Red Hat, Inc.
 * Distributed under license by Red Hat, Inc. All rights reserved.
 * This program is made available under the terms of the
 * Eclipse Public License v2.0 which accompanies this distribution,
 * and is available at https://www.eclipse.org/legal/epl-v20.html
 *
 * Contributors:
 * Red Hat, Inc. - initial API and implementation
 ******************************************************************************/
package com.redhat.devtools.intellij.commonuitest;

import java.awt.event.KeyEvent;
import java.io.FileInputStream;
import java.time.Duration;
import java.util.List;
import java.util.Properties;

import org.cienet.ide_intellij_automation_test.util.TimeUtil;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import com.intellij.remoterobot.RemoteRobot;
import com.intellij.remoterobot.fixtures.CommonContainerFixture;
import com.intellij.remoterobot.fixtures.JListFixture;
import com.intellij.remoterobot.fixtures.dataExtractor.RemoteText;
import static com.intellij.remoterobot.search.locators.Locators.byXpath;
import com.intellij.remoterobot.utils.Keyboard;
import com.intellij.remoterobot.utils.WaitForConditionTimeoutException;
import com.redhat.devtools.intellij.commonuitest.fixtures.dialogs.FlatWelcomeFrame;
import com.redhat.devtools.intellij.commonuitest.fixtures.dialogs.project.NewProjectDialogWizard;
import com.redhat.devtools.intellij.commonuitest.fixtures.mainidewindow.MainIdeWindow;
import com.redhat.devtools.intellij.commonuitest.utils.constants.XPathDefinitions;
import com.redhat.devtools.intellij.commonuitest.utils.runner.IntelliJVersion;
import com.redhat.devtools.intellij.commonuitest.utils.testextension.ScreenshotAfterTestFailExtension;

import org.cienet.ide_intellij_automation_test.util.TimeUtil;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.extension.ExtendWith;

import java.time.Duration;
import java.util.Optional;
import java.util.Properties;
import static com.intellij.remoterobot.search.locators.Locators.byXpath;

import java.awt.event.KeyEvent;
import java.io.FileInputStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

/**
 * Base class for all JUnit tests in the IntelliJ common UI test library
 *
 * <AUTHOR>
 */
// @ExtendWith(ScreenshotAfterTestFailExtension.class)
public abstract class AbstractLibraryBaseTest {
  private static Logger log = LoggerFactory.getLogger(AbstractLibraryBaseTest.class);
  private static IntelliJVersion communityIdeaVersion = AbstractLibraryBaseTest.loadIntellijVersion();
  // private static final IntelliJVersion communityIdeaVersion = IntelliJVersion
  // .getFromStringVersion(System.getProperty("communityIdeaVersion"));
  protected static RemoteRobot remoteRobot;
  protected static int ideaVersionInt;
  
  @Value("${port.runner}")
  private static String portRunner;

  public static IntelliJVersion loadIntellijVersion() {
    try {
      String version = System.getProperty("ideaVersion");
      if (version != null) {
        log.info("Loaded IntelliJ version: " + version);
        return IntelliJVersion.getFromStringVersion(version);
      } else {
        throw new RuntimeException("ideaVersion not found in gradle.properties");
      }
    } catch (Exception e) {
      log.error("Failed to read IntelliJ version : " + e.getMessage());
      throw new RuntimeException(e);
    }
  }

  @BeforeAll
  static void startIntelliJ() {
    // if (!intelliJHasStarted || remoteRobot == null) {
    ideaVersionInt = communityIdeaVersion.toInt();
    int port = portRunner != null ? Integer.parseInt(portRunner) : 8580;
    remoteRobot = UITestRunner.runIde(communityIdeaVersion, port);

    Runtime.getRuntime().addShutdownHook(new CloseIntelliJBeforeQuit());

    // cleanAfterTestFail(remoteRobot);
    // FlatWelcomeFrame flatWelcomeFrame = remoteRobot.find(FlatWelcomeFrame.class,
    // Duration.ofSeconds(10));
    // flatWelcomeFrame.clearWorkspace();
    // flatWelcomeFrame.disableNotifications();
    // }
    // cleanAfterTestFail(remoteRobot);
    // CreateCloseUtils.openProjectFromWelcomeDialog(remoteRobot,
    // "test-plan-resource");
  }

  @AfterAll
  static void finishTestRun() {
    // FlatWelcomeFrame flatWelcomeFrame = remoteRobot.find(FlatWelcomeFrame.class,
    // Duration.ofSeconds(10));
    // flatWelcomeFrame.clearExceptions();
    // flatWelcomeFrame.clearWorkspace();
  }

  // protected void prepareWorkspace(String projectName) {
  // CreateCloseUtils.createNewProject(remoteRobot, projectName,
  // CreateCloseUtils.NewProjectType.PLAIN_JAVA);
  // MainIdeWindow mainIdeWindow = remoteRobot.find(MainIdeWindow.class,
  // Duration.ofSeconds(10));
  // mainIdeWindow.closeProject();
  // }

  protected void openResourceProjectForTest(String... fileNameToPerformTest) {
    CommonContainerFixture mainIdeWindow = remoteRobot.find(CommonContainerFixture.class,
        byXpath(XPathDefinitions.MAIN_IDE_WINDOW),
        Duration.ofSeconds(30));
    mainIdeWindow.click();
    Keyboard keyboard = new Keyboard(remoteRobot);

    for (String file : fileNameToPerformTest) {
      log.info("Executing Ctrl+Shift+N to create new file...");
      if (remoteRobot.isMac())
      {
        keyboard.hotKey(KeyEvent.VK_SHIFT, KeyEvent.VK_META, KeyEvent.VK_O);
      }
      keyboard.hotKey(KeyEvent.VK_CONTROL, KeyEvent.VK_SHIFT, KeyEvent.VK_N);

      // Increase wait time to ensure IntelliJ has processed the action
      TimeUtil.doDelay(500);
      log.info("Waiting for new file dialog to appear...");

      keyboard.enterText(file, 5);
      TimeUtil.doDelay(1000);
      keyboard.enter();
      TimeUtil.doDelay(500);
    }
  }

  private static class CloseIntelliJBeforeQuit extends Thread {
    @Override
    public void run() {
      UITestRunner.closeIde();
    }
  }

  protected static void cleanAfterTestFail(RemoteRobot remoteRobot) {
    // New Project Dialog is visible -> close it
    try {
      NewProjectDialogWizard newProjectDialogWizard = remoteRobot.find(NewProjectDialogWizard.class,
          Duration.ofSeconds(10));
      newProjectDialogWizard.cancel();
      return;
    } catch (WaitForConditionTimeoutException e2) {
      // New Project Dialog is not visible -> continue
    }

    // Flat Welcome Frame dialog is visible -> return
    try {
      remoteRobot.find(FlatWelcomeFrame.class, Duration.ofSeconds(10));
    } catch (WaitForConditionTimeoutException e) {
      // Main IDE Window is visible -> close it
      try {
        remoteRobot.find(MainIdeWindow.class, Duration.ofSeconds(10)).closeProject();
      } catch (WaitForConditionTimeoutException e2) {
        // Main IDE Window is not visible -> continue
      }
    }
  }

  protected void closeAllCurrentTabs() {
    try {
      MainIdeWindow mainIdeWindow = remoteRobot.find(MainIdeWindow.class,
          Duration.ofSeconds(10));

      CommonContainerFixture tabOptionButton;
      try {
        tabOptionButton = mainIdeWindow.find(
            CommonContainerFixture.class,
            byXpath("//div[contains(@myvisibleactions, 'Tab')]"),
            Duration.ofSeconds(3));
      } catch (Exception e) {
        openResourceProjectForTest("Chat/empty.py");
        tabOptionButton = mainIdeWindow.find(
            CommonContainerFixture.class,
            byXpath("//div[contains(@myvisibleactions, 'Tab')]"),
            Duration.ofSeconds(3));
      }

      tabOptionButton.click();
      JListFixture optionList = mainIdeWindow.find(JListFixture.class,
          byXpath(XPathDefinitions.MY_LIST));

      optionList.findText("Close All Tabs").click();

    } catch (Exception e) {
      log.info("not found any tabs open");
    }
  }

  public static void startGeminiTest() {
    CommonContainerFixture selectProject = remoteRobot.find(
        CommonContainerFixture.class,
        byXpath("//div[@javaclass='javax.swing.JEditorPane']"),
        Duration.ofSeconds(10));
    selectProject.findText("Select a Google Cloud project").click();
    try {
      Thread.sleep(5000);
    } catch (InterruptedException e) {
      log.warn("Sleep interrupted: " + e.getMessage());
    }
    CommonContainerFixture selectProject1 = remoteRobot.find(
        CommonContainerFixture.class,
        byXpath("//div[@class='JBTable']"),
        Duration.ofSeconds(30));
    selectProject1.findText("ai-dev-preview-external").click();

    CommonContainerFixture okButton = remoteRobot.find(
        CommonContainerFixture.class,
        byXpath("//div[@text='OK']"),
        Duration.ofSeconds(10));
    okButton.click();

    try {
      Thread.sleep(5000);
    } catch (InterruptedException e) {
      log.warn("Sleep interrupted: " + e.getMessage());
    }

    CommonContainerFixture gotItButton = remoteRobot.find(
        CommonContainerFixture.class,
        byXpath(
            "//div[@text='Got It']"),
        Duration.ofSeconds(10));
    gotItButton.click();

    CommonContainerFixture closeNotiButton = remoteRobot.find(
        CommonContainerFixture.class,
        byXpath(
            "//div[@myicon='close.svg']"),
        Duration.ofSeconds(10));
    closeNotiButton.click();
  }

  public static void selectLines(int lineNumFrom, int lineNumTo) {
    Keyboard keyboard = new Keyboard(remoteRobot);
    keyboard.key(KeyEvent.VK_PAGE_UP);
    for (int i = 1; i < lineNumFrom; i++) {
      keyboard.key(KeyEvent.VK_DOWN);
      TimeUtil.doDelay(50);
    }
    keyboard.key(KeyEvent.VK_HOME);
    TimeUtil.doDelay(50);
    // Select lines
    int linesToSelect = lineNumTo - lineNumFrom;
    for (int i = 0; i < linesToSelect + 1; i++) {
      keyboard.hotKey(KeyEvent.VK_SHIFT, KeyEvent.VK_DOWN);
      TimeUtil.doDelay(150);
    }
  }
  
    protected void checkGeminiActiveOrFail() {
        MainIdeWindow mainIdeWindow = remoteRobot.find(MainIdeWindow.class, Duration.ofSeconds(10));
        CommonContainerFixture geminiButton = mainIdeWindow.find(
                CommonContainerFixture.class,
                byXpath("//div[@class='IconPresentationComponent']"),
                Duration.ofSeconds(5));
        geminiButton.click();
        
        JListFixture myList = mainIdeWindow.find(
                JListFixture.class,
                byXpath("//div[@class='MyList']"),
                Duration.ofSeconds(5));
        boolean isInactive = false;
        
        List<RemoteText> items = myList.findAllText();
        for (RemoteText item : items) {
            if (item.getText().contains("Completion inactive")) {
                isInactive = true;
                break;
            }
        }
        if (isInactive) {
            throw new AssertionError("Test failed: Gemini is not active");
        }       
    }
}
