package org.cienet.ide_intellij_automation_test.test.codeTransform;

import static com.intellij.remoterobot.search.locators.Locators.byXpath;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.Duration;

import org.cienet.ide_intellij_automation_test.test.BaseTest;
import org.cienet.ide_intellij_automation_test.util.CommonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.intellij.remoterobot.fixtures.CommonContainerFixture;
import com.intellij.remoterobot.fixtures.EditorFixture;
import com.intellij.remoterobot.fixtures.JPopupMenuFixture;
import com.intellij.remoterobot.fixtures.JTextFieldFixture;
import com.intellij.remoterobot.utils.Keyboard;

public class BaseTransformTest extends BaseTest {
    private static final Logger log = LoggerFactory.getLogger(BaseTransformTest.class);

    protected EditorFixture openInEditorPrompt(String filename, String fileContent) {
        closeAllCurrentTabs();
        openMultiplesFiles(filename);

        EditorFixture editor = CommonUtil.prepareEditor(remoteRobot);

        if (fileContent != null) {
            editor.setText("");
            editor.click();

            // Use clipboard to set entire text content at once
            Keyboard keyboard = new Keyboard(remoteRobot);
            CommonUtil.setTextUsingClipboard(keyboard, fileContent);
        }

        editor.rightClick();
        var menu = remoteRobot.find(
                JPopupMenuFixture.class,
                byXpath("//div[@class='HeavyWeightWindow']"),
                Duration.ofSeconds(10));
        menu.findText("Gemini").click();

        var generateMenu = remoteRobot.find(
                CommonContainerFixture.class,
                byXpath("//div[@text='Gemini']//div[@text='Open In-Editor Prompt...']"),
                Duration.ofSeconds(10));
        generateMenu.click();

        var jbTextField = remoteRobot.find(
                JTextFieldFixture.class,
                byXpath("//div[@visible_text='visibleText' and @class='JBTextField']".replace("visibleText",
                        "Type / for commands or chat with Gemini Code Assist")),
                Duration.ofSeconds(10));
        jbTextField.click();

        return editor;
    }

    protected void doSimpleTransformTest(String filename, String fileContent, String prompt) {
        log.info("Start execute transform test");

        var editor = openInEditorPrompt(filename, fileContent);

        Keyboard keyboard = new Keyboard(remoteRobot);
        keyboard.enterText(prompt, 10);

        int startLength = editor.getText().length();
        keyboard.enter();

        var highlightButton = remoteRobot.find(
                CommonContainerFixture.class,
                byXpath("//div[@class='HighlightedDiffActionButton']"),
                Duration.ofSeconds(30));
        highlightButton.click();

        String finalText = editor.getText();
        int finalLength = finalText.trim().length();

        assertTrue(finalLength != startLength,
                String.format("Code transform failed. Prompt:\n%s\n", finalText));
    }

    protected void openMultiplesFiles(String currentFile) {
        openResourceProjectForTest(currentFile);
    }
}
