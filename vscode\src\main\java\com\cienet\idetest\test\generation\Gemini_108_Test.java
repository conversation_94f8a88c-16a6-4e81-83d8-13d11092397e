package com.cienet.idetest.test.generation;

import com.cienet.idetest.util.TestExceptionHandler;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.IOException;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_108_Test extends BaseGenerationTest {

    @Test
    void testCodeGen() throws IOException {
        String text = "// Given an array of integers nums, sort the array in ascending order using quick sort and return it";
        String filePath = "Code Generation/Go/quick_sort.go";
        testCodeGenByText(filePath, text);
    }
}
