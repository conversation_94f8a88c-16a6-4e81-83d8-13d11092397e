package com.cienet.idetest.util;

import lombok.extern.slf4j.Slf4j;

import java.awt.*;
import java.awt.event.KeyEvent;

@Slf4j
public class RobotUtil {

    private static Robot robot = null;

    // Robot mus be initialized in the beginning,
    // If you create a robot instance, it will grab the focus from vscode.
    public static void init() {
        try {
            robot = new Robot();
            // If the mouse stays in the vscode editing area, there may be problems when triggering
            // the context menu. The menu item at the mouse position will be automatically selected.
            robot.mouseMove(0,0);
        } catch (AWTException e) {
            throw new RuntimeException(e);
        }
    }

    public static void handleFileChooser(String filePath) {
        // Wait for the File Chooser to appear
        TimeUtil.doDelay(2000);
        robot.keyPress(KeyEvent.VK_SLASH);
        robot.keyRelease(KeyEvent.VK_SLASH);
        TimeUtil.doDelay(2000);
        robot.keyPress(KeyEvent.VK_BACK_SPACE);
        robot.keyRelease(KeyEvent.VK_BACK_SPACE);
        TimeUtil.doDelay(1000);

        // Type the file path
        for (char c : filePath.toCharArray()) {
            int keyCode = KeyEvent.getExtendedKeyCodeForChar(c);
            if (KeyEvent.CHAR_UNDEFINED == keyCode) {
                throw new RuntimeException("Key code not found for character: " + c);
            }
            robot.keyPress(keyCode);
            robot.keyRelease(keyCode);
            TimeUtil.doDelay(100); // Small delay between key presses
        }

        TimeUtil.doDelay(1000);
        // Press Enter to confirm
        robot.keyPress(KeyEvent.VK_ENTER);
        robot.keyRelease(KeyEvent.VK_ENTER);

        TimeUtil.doDelay(1000);
        // Press Enter to confirm again
        robot.keyPress(KeyEvent.VK_ENTER);
        robot.keyRelease(KeyEvent.VK_ENTER);
    }

    public static void clickChatInCtxMenu() {
        clickGeminiInCtxMenu();

        // Press the "arrow down" key 2 times
        for (int i = 0; i < 2; i++) {
            robot.keyPress(KeyEvent.VK_DOWN);
            robot.keyRelease(KeyEvent.VK_DOWN);
            TimeUtil.doDelay(200); // Delay of 0.2 seconds
        }

        // Press the "Enter" key
        robot.keyPress(KeyEvent.VK_ENTER);
        robot.keyRelease(KeyEvent.VK_ENTER);
    }

    public static void clickGenerateInCtxMenu() {
        clickGeminiInCtxMenu();
        // Press the "Enter" key
        robot.keyPress(KeyEvent.VK_ENTER);
        robot.keyRelease(KeyEvent.VK_ENTER);
    }

    public static void clickGenerateUnitTestInCtxMenu() {
        clickGeminiInCtxMenu();
        robot.keyPress(KeyEvent.VK_DOWN);
        robot.keyRelease(KeyEvent.VK_DOWN);
        TimeUtil.doDelay(200); // Delay of 0.2 seconds
        // Press the "Enter" key
        robot.keyPress(KeyEvent.VK_ENTER);
        robot.keyRelease(KeyEvent.VK_ENTER);
    }

    public static void clickGeminiInCtxMenu() {
        // Make sure .git directory is not in canonical-answer and test-plan-resource source code directories.
        // Otherwise, there will be an extra item 'share' in context menu.
        // int downClicks = CommonUtil.isMacOs() && CommonUtil.isMacOsAbove14() ? 7 : 6;
        int downClicks = 7;
        String overrideDownClicks = System.getProperty("uitest.context.menu.down.clicks");

        if (overrideDownClicks != null) {
            log.info("Override down clicks: {}", overrideDownClicks);
            downClicks = Integer.parseInt(overrideDownClicks);
        }

        log.debug("Down click times: {}, osType: {}", downClicks, CommonUtil.getOsTyppe());

        for (int i = 0; i < downClicks; i++) {
            robot.keyPress(KeyEvent.VK_DOWN);
            robot.keyRelease(KeyEvent.VK_DOWN);
            TimeUtil.doDelay(200); // Delay of 0.2 seconds
        }

        // Press the "arrow right" key 1 time to the sub-menu: 1.Generate code 2.Generate unit test 3.Explain this
        robot.keyPress(KeyEvent.VK_RIGHT);
        robot.keyRelease(KeyEvent.VK_RIGHT);
        TimeUtil.doDelay(200); // Delay of 0.2 seconds
    }

    public static void clickEscKey() {
        robot.keyPress(KeyEvent.VK_ESCAPE);
        robot.keyRelease(KeyEvent.VK_ESCAPE);
        TimeUtil.doDelay(200); // Delay of 0.2 seconds
    }

    public static void clickTabKey() {
        robot.keyPress(KeyEvent.VK_TAB);
        robot.keyRelease(KeyEvent.VK_TAB);
        TimeUtil.doDelay(200); // Delay of 0.2 seconds
    }

    /**
     * Enter text through the keyboard
     * @param text
     */
    public static void enterText(String text) {
        for (char c : text.toCharArray()) {
            int keyCode = KeyEvent.getExtendedKeyCodeForChar(c);
            if (KeyEvent.CHAR_UNDEFINED == keyCode) {
                throw new RuntimeException("Key code not found for character: " + c);
            }
            robot.keyPress(keyCode);
            robot.keyRelease(keyCode);
            TimeUtil.doDelay(100); // Small delay between key presses
        }
    }

    public static void clickEnterKey() {
        robot.keyPress(KeyEvent.VK_ENTER);
        robot.keyRelease(KeyEvent.VK_ENTER);
        TimeUtil.doDelay(200); // Delay of 0.2 seconds
    }

    public static void clickCtrlAndKey(int keyCode) {
        robot.keyPress(KeyEvent.VK_CONTROL);
        robot.keyPress(keyCode);
        robot.keyRelease(keyCode);
        robot.keyRelease(KeyEvent.VK_CONTROL);
        TimeUtil.doDelay(200); // Delay of 0.2 seconds
    }

    public static void clickFullScreenKey() {
        switch (CommonUtil.getOsTyppe()) {
            case Mac -> {
                robot.keyPress(KeyEvent.VK_CONTROL);
                TimeUtil.doDelay(100);
                robot.keyPress(KeyEvent.VK_META); // Command key on Mac
                TimeUtil.doDelay(100);
                robot.keyPress(KeyEvent.VK_F);
                TimeUtil.doDelay(200);

                robot.keyRelease(KeyEvent.VK_F);
                TimeUtil.doDelay(100);
                robot.keyRelease(KeyEvent.VK_META);
                TimeUtil.doDelay(100);
                robot.keyRelease(KeyEvent.VK_CONTROL);
            }
//            case Linux -> {
//                // F11 down -> F11 up
//                YDoToolUtil.keyPress("87:1", "87:0");
//            }
            default -> {
                robot.keyPress(KeyEvent.VK_F11);
                TimeUtil.doDelay(100);
                robot.keyRelease(KeyEvent.VK_F11);
            }
        }
    }

    private static void doMouseMove(int x, int y) {
//        if (CommonUtil.isLinux()) {
//            YDoToolUtil.mouseMove("-a", String.valueOf(x), String.valueOf(y));
//        } else {
            robot.mouseMove(x, y);
//        }
    }

    public static void mouseMove(int x, int y) {
        doMouseMove(x, y);
    }

    public static void mouseMove(int x1, int y1, int x2, int y2, int xstep, int ystep, long delay) {
        int x = x1, y = y1;
        boolean xarrive = (x1 == x2);
        boolean yarrive = (y1 == y2);
        doMouseMove(x, y);
        while (true) {
            TimeUtil.doDelay(delay);
            if (xarrive && yarrive) {
                return;  // move finish
            }
            x = x + xstep;
            y = y + ystep;
            if (x2 > x1) {
                xarrive = (x >= x2);
            }
            if (y2 > y1) {
                yarrive = (y >= y2);
            }
            doMouseMove(x, y);
        }
    }
}
