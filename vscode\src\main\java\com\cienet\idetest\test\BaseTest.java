package com.cienet.idetest.test;

import com.cienet.idetest.logging.DebugLogFilter;
import com.cienet.idetest.test.ui.EditorArea;
import com.cienet.idetest.test.ui.TelemetryPanel;
import com.cienet.idetest.util.*;
import com.cienet.idetest.vo.TestConstant;
import com.cienet.ide.test.common.vo.VersionInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.devtools.DevTools;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.support.ui.WebDriverWait;
import com.cienet.ide.test.common.util.ScreenRecordHelper;

import java.time.Duration;
import java.util.Timer;
import java.util.TimerTask;

@Slf4j
public class BaseTest {

    private Boolean videoEnabled;
    private ScreenRecordHelper recordHelper;

    private static int VERSION_INFO_INTERVAL = 60 * 60 * 1000; // Invalidate interval 60 minutes.

    private static final Timer invalidateTimer = new Timer();

    private static VersionInfo versionInfo = null;

    protected WebDriver driver;
    protected WebDriverWait wait;
    protected WebDriverWait shortWait;
    protected WebDriverWait longWait;
    protected Actions actions;

    static {
        log.info("Schedule a timer to invalidate VersionInfo.");

        invalidateTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                log.debug("Invalidate VersionInfo. current versionInfo: {}", versionInfo);
                versionInfo = null;
            }
        }, VERSION_INFO_INTERVAL, VERSION_INFO_INTERVAL);
    }

    @BeforeEach
    protected void setup() {
        String testName = getClass().getSimpleName();
        DebugLogFilter.startTestLogging(testName);
        RobotUtil.init();

        if (isVideoEnabled()) {
            recordHelper = new ScreenRecordHelper();
            try {
                recordHelper.startRecord(testName, SessionUtil.getResultOid());
            } catch (Exception e) {
                log.error("Failed to start screen record.", e);
                // ignored
            }
        }

        driver = VSCodeUtil.getChromeDriver();
        wait = new WebDriverWait(driver, Duration.ofSeconds(TestConstant.WEBDRIVER_WAIT_TIME_NORMAL));
        shortWait = new WebDriverWait(driver, Duration.ofSeconds(TestConstant.WEBDRIVER_WAIT_TIME_SHORT));
        longWait = new WebDriverWait(driver, Duration.ofSeconds(TestConstant.WEBDRIVER_WAIT_TIME_LONG));
        UiUtil.prepareVscForTest(driver, wait);
        actions = new Actions(driver);
        getIdePluginVersion();
        TimeUtil.doDelay(1000);
    }

    protected boolean isVideoEnabled() {
        if (videoEnabled != null) {
            return videoEnabled;
        }
        String enableScreeRecord = System.getProperty("uitest.enable.screen.record");
        // Enable screen record by default.
        boolean enabled = true;

        if (StringUtils.isNotEmpty(enableScreeRecord)) {
            enabled = Boolean.valueOf(enableScreeRecord);
        }

        log.info("Enable screen record: {}.", enabled);
        videoEnabled = enabled;
        return videoEnabled;
    }

    protected VersionInfo getIdePluginVersion() {
        if (versionInfo != null && versionInfo.bothValuePresented()) {
            return versionInfo;
        }
        TelemetryPanel telemetry = new TelemetryPanel(driver, wait, actions);
        telemetry.open();
        TimeUtil.doDelay(2000);
        telemetry.maximize();
        versionInfo = new VersionInfo(telemetry.getIdeVersion(), telemetry.getPluginVersion());
        telemetry.close();
        log.info("Got versionInfo: {} from telemetry.", versionInfo);
        return versionInfo;
    }

    /**
     * Static getter method for returning TestSummary to the client
     */
    public static VersionInfo getVersionInfo() {
        return versionInfo;
    }

    @AfterEach
    protected void tearDown() {
        VSCodeUtil.closeDriver(driver);
        DebugLogFilter.stopTestLogging();

        if (recordHelper != null) {
            try {
                recordHelper.stopRecord();
            } catch (Exception e) {
                log.error("Failed to stop screen record.", e);
                // ignored
            }
        }
    }

    protected void clearEditorText() {
        EditorArea editorArea = new EditorArea(driver, wait, actions);
        editorArea.clearEditorText();
    }

    protected void testCdp() {
        ChromeDriver driver = (ChromeDriver) VSCodeUtil.getChromeDriver(); // Assuming getChromeDriver() is your method
        DevTools devTools = driver.getDevTools();
        devTools.createSession();
    }

}
