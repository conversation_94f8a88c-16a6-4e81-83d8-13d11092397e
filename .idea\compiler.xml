<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Gradle Imported" enabled="true">
        <outputRelativeToContentRoot value="true" />
        <processorPath useClasspath="false">
          <entry name="$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.projectlombok/lombok/1.18.32/17d46b3e205515e1e8efd3ee4d57ce8018914163/lombok-1.18.32.jar" />
        </processorPath>
        <module name="ide-test-automation.main" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel target="17" />
  </component>
</project>