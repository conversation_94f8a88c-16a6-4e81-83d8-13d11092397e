package org.cienet.ide_intellij_automation_test.util;

import com.intellij.remoterobot.RemoteRobot;

public class SessionUtil {

    private static final ThreadLocal<RemoteRobot> driverThreadLocal = new ThreadLocal<>();

    private static final ThreadLocal<String> resultOidThreadLocal = new ThreadLocal<>();

    private static final ThreadLocal<String> testNameThreadLocal = new ThreadLocal<>();

    public static RemoteRobot getRemoteRobot() {
        return driverThreadLocal.get();
    }

    public static void deleteAll() {
        deleteRemoteRobot();
        deleteResultOid();
        deleteTestName();
    }

    public static void setRemoteRobot(RemoteRobot remoteRobot) {
        driverThreadLocal.set(remoteRobot);
    }

    public static void deleteRemoteRobot() {
        driverThreadLocal.remove();
    }

    public static String getResultOid() {
        return resultOidThreadLocal.get();
    }

    public static void setResultOid(String resultOid) {
        resultOidThreadLocal.set(resultOid);
    }

    public static void deleteResultOid() {
        resultOidThreadLocal.remove();
    }

    public static String getTestName() {
        return testNameThreadLocal.get();
    }

    public static void setTestName(String testName) {
        testNameThreadLocal.set(testName);
    }

    public static void deleteTestName() {
        testNameThreadLocal.remove();
    }
}
