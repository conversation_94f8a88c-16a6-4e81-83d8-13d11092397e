package com.cienet.idetest.util;

import org.openqa.selenium.By;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.FluentWait;
import org.openqa.selenium.support.ui.Wait;

import java.time.Duration;

public class WaitUtil {

    private static void waitUntilElementIsGone(WebDriver driver, By locator, int timeout, int pollingInterval) {
        Wait<WebDriver> wait = new FluentWait<>(driver)
                .withTimeout(Duration.ofSeconds(timeout))
                .pollingEvery(Duration.ofMillis(pollingInterval))
                .ignoring(NoSuchElementException.class);

        wait.until(ExpectedConditions.invisibilityOfElementLocated(locator));
    }

    public static void waitUntilElementIsGone(WebDriver driver, By locator, int timeout) {
        waitUntilElementIsGone(driver, locator, timeout, 500);
    }

}