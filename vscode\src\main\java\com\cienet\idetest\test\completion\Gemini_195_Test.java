package com.cienet.idetest.test.completion;

import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;

import java.io.IOException;
import java.util.List;

import static com.cienet.idetest.util.UiUtil.findElement;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Deprecated // This case need to idle for 1 hour.
@ExtendWith(TestExceptionHandler.class)
public class Gemini_195_Test extends BaseCompletionTest {

    @Test
    public void test() throws IOException {
        // Given
        UiUtil.openResourceProjectForTest(driver, wait, "Chat/two_sum.py");
        TimeUtil.doDelayInMinutes(1);
        clearEditorText();

        // When
        actions.sendKeys("def add(a, b : int) -> int:").perform();
        actions.sendKeys(Keys.ENTER).perform();
        WebElement acceptWordButton = findElement(wait, By.xpath("//*[contains(text(),'Accept')]"));
        acceptWordButton.click();
        TimeUtil.doDelay(1000);
        List<WebElement> codeLines = driver.findElements(By.className("view-line"));

        // Then
        assertTrue(codeLines.size() >= 2);
        assertTrue(codeLines.stream().anyMatch(line -> line.getText().contains("return")));
    }

}