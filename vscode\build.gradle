plugins {
    id 'java'
    id 'org.springframework.boot' version '3.2.5'
    id 'io.spring.dependency-management' version '1.1.4'
}

group = 'org.cienet'
version = '0.0.1-SNAPSHOT'

java {
    sourceCompatibility = '21'
}

repositories {
    mavenCentral()
    google()
}

dependencies {
    // Shared library with common classes
    implementation 'org.cienet:ide-test-shared'
    
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.boot:spring-boot-starter-web:3.3.0'
    implementation group: 'org.springdoc', name: 'springdoc-openapi-starter-webmvc-ui', version: '2.0.3'
    implementation group: 'org.springdoc', name: 'springdoc-openapi-ui', version: '1.6.15'
    implementation 'org.reflections:reflections:0.10.2'

    // Selenium and WebDriver Manager
    implementation group: 'org.seleniumhq.selenium', name: 'selenium-java', version: '4.28.1'
    implementation group: 'io.github.bonigarcia', name: 'webdrivermanager', version: '5.7.0'

    // JUnit 5 dependencies
    implementation 'org.springframework.boot:spring-boot-starter-test'
    implementation 'org.junit.jupiter:junit-jupiter-api'
    implementation 'org.junit.platform:junit-platform-launcher'
    testImplementation group: 'junit', name: 'junit', version: '4.13.2'
    implementation group: 'junit', name: 'junit', version: '4.13.2'

    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine'
    implementation 'org.junit.jupiter:junit-jupiter-engine'

    compileOnly 'org.projectlombok:lombok:1.18.32'
    annotationProcessor 'org.projectlombok:lombok:1.18.32'
}

tasks.named('test') {
    useJUnitPlatform()
}

tasks.register('prodTar', Tar) {
    archiveFileName = 'vsc.agent.prod.tar.gz'
    destinationDirectory = layout.buildDirectory.dir("tar")
    compression = Compression.GZIP

    // Include files matching startagent.*
    from('.') {
        include 'startagent.*'
    }

    // Include jar files from build/libs
    from('build/libs') {
        include '*.jar'
        into 'libs'
    }

    // Include ../test-plan-resource into a subdirectory named "test-plan-resource"
    from('../../test-plan-resource') {
        exclude '.git/**'
        into 'test-plan-resource'
    }

}.configure {
    dependsOn tasks.named('build')
}
