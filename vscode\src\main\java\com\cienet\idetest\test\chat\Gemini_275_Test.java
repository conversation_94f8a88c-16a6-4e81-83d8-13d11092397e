package com.cienet.idetest.test.chat;

import com.cienet.idetest.test.ui.BottomBar;
import com.cienet.idetest.test.ui.ChatWindow;
import com.cienet.idetest.test.ui.PopUpWindow;
import com.cienet.idetest.util.TestExceptionHandler;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_275_Test extends BaseChatTest {

    /**
     * Gemini-275 : Select Gemini project from Chat - Version3
     * To verify whether user gets notification on chat window to login when Gemini project is not selected
     *
     * Test compared with TesLink on: 3/10/2024
     */
    @Test
    void test() {
        // Given
        BottomBar bottomBar = new BottomBar(driver, wait);

        // When
        PopUpWindow popUpWindow = bottomBar.googleConsoleStatus();
        popUpWindow.selectGoogleCloudProject();

        ChatWindow chatWindow = bottomBar.openChatWindow();
        chatWindow.stepInto();

        // Then
        assertTrue(chatWindow.getChatHelloMessage().isDisplayed());
    }

}
