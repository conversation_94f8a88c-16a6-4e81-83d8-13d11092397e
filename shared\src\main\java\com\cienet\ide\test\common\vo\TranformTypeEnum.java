package com.cienet.ide.test.common.vo;

public enum TranformTypeEnum {
    None(""),
    Fix("/fix"),
    Generate("/generate"),
    Doc("/doc"),
    Simplify("/simplify");

    private static TranformTypeEnum[] allEnums = {
            None,
            Fix,
            Generate,
            Doc,
            Simplify,
    };

    private final String value;

    private TranformTypeEnum(String value) {
        this.value = value;
    }

    public static TranformTypeEnum[] getAllEnums() {
        return allEnums;
    }

    public String value() {
        return value;
    }
}
