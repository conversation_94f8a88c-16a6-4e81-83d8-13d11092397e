package com.cienet.idetest.web;

import com.cienet.idetest.service.TestRunnerService;
import com.cienet.idetest.util.CommonUtil;
import com.cienet.idetest.util.SessionUtil;
import com.cienet.ide.test.common.vo.TestSummary;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@RestController
@RequestMapping("/tests")
public class TestRunController {

    private static Lock lock = new ReentrantLock();

    @Autowired
    private TestRunnerService testRunnerService;

    @GetMapping(path = "/health")
    public String health() {
        return "OK";
    }

    @GetMapping(path = "/run/{testName}")
    public TestSummary runTestByName(@PathVariable String testName) {
        return runTestByName(testName, "NULL");
    }

    @GetMapping(path = "/run/{testName}/{resultOid}")
    public TestSummary runTestByName(@PathVariable String testName, @PathVariable String resultOid) {
        if (lock.tryLock()) {
            try {
                SessionUtil.setTestName(testName);
                SessionUtil.setResultOid(resultOid);
                return testRunnerService.runTestByTestName(testName);
            } finally {
                lock.unlock();
            }
        } else {
            TestSummary testSummary = new TestSummary();
            testSummary.setTestName(testName);
            testSummary.setTestResult(TestSummary.TestResult.Failed);
            testSummary.setExecutionTime(-1l); // to recognize the fail is due to concurrent test requests
            return testSummary;
        }
    }

    @GetMapping(path = "/getAvailableTestCases")
    public List<String> getAvailableTestCases() {
        return testRunnerService.getAvailableTestCases();
    }

    @GetMapping(path = "/log")
    public ResponseEntity<Resource> getLogFile() {
        try {
            String currentPath = CommonUtil.getCurrentDir();
            Path logFilePath = Paths.get(currentPath + "/logs").resolve("spring-boot-app.log").normalize();

            // Check if the file exists
            if (!Files.exists(logFilePath)) {
                return ResponseEntity.notFound().build();
            }

            // Load the file as a resource
            Resource resource = new UrlResource(logFilePath.toUri());

            // Set the content type and headers for file download
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
        } catch (Exception e) {
            // Handle exceptions (e.g., file not found, access issues)
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get the video file of the test case starts with {resultOid}_{testCaseName}.
     * If there are more than one files, it will get the latest one.
     */
    @GetMapping(path = "/video/{testCaseName}/{resultOid}")
    public ResponseEntity<Resource> getVideo(@PathVariable String testCaseName, @PathVariable String resultOid) {
        try {
            String currentPath = CommonUtil.getCurrentDir();
            Path videoDirPath = Paths.get(currentPath + "/videos").normalize();

            // Check if the directory exists
            if (!Files.exists(videoDirPath) || !Files.isDirectory(videoDirPath)) {
                return ResponseEntity.notFound().build();
            }

            // Find all matching files
            Path latestFile = Files.list(videoDirPath)
                    .filter(path -> path.getFileName().toString().startsWith(testCaseName + "_" + resultOid))
                    .filter(Files::isRegularFile)
                    .max((file1, file2) -> {
                        try {
                            return Files.getLastModifiedTime(file1).compareTo(Files.getLastModifiedTime(file2));
                        } catch (Exception e) {
                            return 0; // Handle exception gracefully
                        }
                    })
                    .orElse(null);

            // Check if a matching file was found
            if (latestFile == null) {
                return ResponseEntity.notFound().build();
            }

            // Load the file as a resource
            Resource resource = new UrlResource(latestFile.toUri());

            // Set the content type and headers for file download
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .body(resource);
        } catch (Exception e) {
            // Handle exceptions (e.g., file not found, access issues)
            return ResponseEntity.internalServerError().build();
        }
    }
}
