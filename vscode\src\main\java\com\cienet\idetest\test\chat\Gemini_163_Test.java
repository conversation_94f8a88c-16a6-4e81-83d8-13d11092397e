package com.cienet.idetest.test.chat;

import com.cienet.idetest.test.ui.BottomBar;
import com.cienet.idetest.test.ui.ChatWindow;
import com.cienet.idetest.test.ui.PopUpWindow;
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;

import java.util.List;

import static com.cienet.idetest.util.UiUtil.findElement;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_163_Test extends BaseChatTest {

    /**
     * Gemini-163 : Chat - Window Opens with example prompts - Version4
     * DuetAI chat window provides example prompts
     *
     * Test compared with TesLink on: 3/10/2024
     * Reviewed with test team on 16/10/2024
     */
    @Test
    public void test() {
        // Given
        BottomBar bottomBar = new BottomBar(driver, wait);

        // When
        PopUpWindow popUpWindow = bottomBar.googleConsoleStatus();
        popUpWindow.selectGoogleCloudProject();

        ChatWindow chatWindow = bottomBar.openChatWindow();
        chatWindow.stepInto();
        chatWindow.closeTipsIfVisible();

        WebElement hintMsg = findElement(wait, By.xpath("//*[contains(text(),'Prompts to try')]"));
        assertTrue(hintMsg.isDisplayed());
        WebElement clickableSuggestion = findElement(wait, By.xpath("//*[contains(text(),'How do I use Gemini Code Assist?')]"));
        assertTrue(clickableSuggestion.isDisplayed());
        TimeUtil.doDelayInSeconds(1);
        clickableSuggestion.click();

        // Then
        List<WebElement> chatHistory = chatWindow.waitUntilChatFinishAndGetHistory();
        assertTrue(chatHistory.size() >= 2, "Chat history div does not exist");
    }

}
