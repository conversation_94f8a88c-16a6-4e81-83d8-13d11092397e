package com.cienet.idetest.test.completion;

import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.ide.test.common.vo.LanguageTypeEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.IOException;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_96_Test extends BaseCompletionTest {

    /**
     * Gemini-96
     * Basic sanity test that code completion can be triggered with JS.
     * Only tests if code completion can be triggered and returns a suggestion -
     * does not test the syntax, formatting, or quality of the suggestion.
     *
     * @throws IOException
     */
    @Test
    public void testJsCodeComplete() throws IOException {
        String filePath = "Code Completion/JavaScript/sortArray.js";
        String comment = "// Given an array of integers nums, sort the array in ascending order using quick sort and return it";
        String funcDeclare = "var sortArray = function(nums) {";
        doCompletionTest(filePath, comment, funcDeclare);
    }

}