package org.cienet.ide_intellij_automation_test.util;

import com.intellij.remoterobot.RemoteRobot;
import com.intellij.remoterobot.fixtures.CommonContainerFixture;
import com.intellij.remoterobot.fixtures.ComponentFixture;
import com.intellij.remoterobot.fixtures.JButtonFixture;
import com.intellij.remoterobot.fixtures.JLabelFixture;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.List;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

import static com.intellij.remoterobot.search.locators.Locators.byXpath;

/**
 * Utility class for retrieving IntelliJ IDE version information via Remote
 * Robot
 */
public class IdeVersionUtil {

    private static final Logger log = LoggerFactory.getLogger(IdeVersionUtil.class);
    private static final Duration DEFAULT_TIMEOUT = Duration.ofSeconds(3);

    /**
     * Get IntelliJ IDE version using Remote Robot
     * 
     * @param remoteRobot Remote Robot instance
     * @return IDE version string or null if not found
     */
    public static String getIdeVersion(RemoteRobot remoteRobot) {
        if (remoteRobot == null) {
            log.warn("RemoteRobot is null, cannot retrieve IDE version");
            return null;
        }

        try {
            log.info("Attempting to retrieve IntelliJ IDE version...");

            // Method 1: Try from Help > About dialog
            String version = getVersionFromAboutDialog(remoteRobot);
            if (version != null) {
                log.info("Successfully retrieved IDE version: {}", version);
                return version;
            }

            log.warn("Could not retrieve IntelliJ IDE version");
            return null;

        } catch (Exception e) {
            log.error("Error retrieving IntelliJ IDE version", e);
            return null;
        }
    }

    /**
     * Get IDE version from Help > About dialog
     */
    private static String getVersionFromAboutDialog(RemoteRobot remoteRobot) {
        try {
            log.debug("Trying to get IDE version from About dialog...");

            // Open Help > About
            openAboutDialog(remoteRobot);

            // Look for version information
            String version = extractVersionFromAbout(remoteRobot);

            // Close About dialog
            closeAboutDialog(remoteRobot);

            return version;

        } catch (Exception e) {
            log.debug("Failed to get IDE version from About dialog", e);
            try {
                closeAboutDialog(remoteRobot);
            } catch (Exception closeEx) {
                log.debug("Failed to close About dialog after error", closeEx);
            }
            return null;
        }
    }

    /**
     * Open Help > About dialog
     */
    private static void openAboutDialog(RemoteRobot remoteRobot) {
        try {
            if(remoteRobot.isMac()) {
                remoteRobot.find(CommonContainerFixture.class, byXpath("//div[@class='MainMenuWithButton']")).click();
                var mainMenu = remoteRobot.find(CommonContainerFixture.class,
                byXpath("//div[@class='MyList' and contains(@visible_text, 'Help')]"),
                DEFAULT_TIMEOUT);
                mainMenu.findText("Help").moveMouse();

                var subMenu = remoteRobot.find(CommonContainerFixture.class,
                byXpath("//div[@class='MyList' and contains(@visible_text, 'Register…')]"),
                DEFAULT_TIMEOUT);
                subMenu.findText("Register…").click();

                remoteRobot.find(CommonContainerFixture.class, byXpath("//div[@class='R' and @myicon='settings.svg']")).click();

                var aboutMenu = remoteRobot.find(CommonContainerFixture.class,
                byXpath("//div[@class='MyList' and contains(@visible_text, 'About')]"),
                DEFAULT_TIMEOUT);
                aboutMenu.findText("About").click();
            } else {
            var mainMenu = remoteRobot.find(CommonContainerFixture.class,
                    byXpath("//div[@tooltiptext='Main Menu']"),
                    DEFAULT_TIMEOUT);
            mainMenu.click();

            // Try Help menu approach
            remoteRobot.find(ComponentFixture.class, byXpath("//div[@text='Help']"), DEFAULT_TIMEOUT).moveMouse();
            TimeUtil.doDelay(500);
            remoteRobot.find(ComponentFixture.class, byXpath(
                    "//div[@accessiblename='Help' and @class='ActionMenu' and @text='Help']//div[@text='About']"),
                    DEFAULT_TIMEOUT).click();
            }
        } catch (Exception e) {
            throw new RuntimeException("Could not open About dialog", e);
        }
    }

    /**
     * Extract version from About dialog
     */
    private static String extractVersionFromAbout(RemoteRobot remoteRobot) {
        try {
            if(remoteRobot.isMac()) {
                // Find the JLabel containing IDE version information
                var versionLabel = remoteRobot.find(JLabelFixture.class,
                        byXpath("//div[contains(@visible_text, 'Edition)')]"),
                        DEFAULT_TIMEOUT);
                
                String versionText = versionLabel.getValue().toString();
                log.debug("Found version text: {}", versionText);
                
                // Extract version number using regex pattern for version like "2025.1.1.1"
                if (versionText != null && versionText.contains("IntelliJ IDEA")) {
                    Pattern versionPattern = Pattern.compile("IntelliJ IDEA (\\d+\\.\\d+\\.\\d+\\.\\d+)");
                    Matcher versionMatcher = versionPattern.matcher(versionText);
                    
                    if (versionMatcher.find()) {
                        String version = versionMatcher.group(1);
                        log.debug("Extracted version number: {}", version);
                        return version;
                    } else {
                        log.debug("No version pattern found, returning full text: {}", versionText);
                        return versionText.trim();
                    }
                }
                
                // Fallback: try to find in JEditorPane if JLabel approach fails
                var borderLayoutPanel = remoteRobot.find(CommonContainerFixture.class,
                        byXpath("//div[@class='DialogRootPane'][.//div[@text='Copy and Close']]"),
                        DEFAULT_TIMEOUT);
                
                List<JLabelFixture> labels = borderLayoutPanel.findAll(JLabelFixture.class,
                        byXpath("//div[@class='JBLabel']//div[@class='JEditorPane']"));

                if (!labels.isEmpty()) {
                    var fullText = labels.get(1).getValue().toString();
                    log.debug("Full text from About dialog: {}", fullText);
                    
                    // Extract build number using regex: starts with # and ends with space or comma
                    Pattern pattern = Pattern.compile("#([^\\s,]+)");
                    Matcher matcher = pattern.matcher(fullText);
                    
                    if (matcher.find()) {
                        String buildNumber = "#" + matcher.group(1);
                        log.debug("Extracted build number: {}", buildNumber);
                        return buildNumber;
                    } else {
                        log.debug("No build number pattern found in: {}", fullText);
                        return fullText;
                    }
                }
                return null;
            }
            else {          
                // Find the JLabel containing IDE version information
                var versionLabel = remoteRobot.find(JLabelFixture.class,
                        byXpath("//div[@class='JBLabel' and contains(@accessiblename, 'IntelliJ IDEA')]"),
                        DEFAULT_TIMEOUT);
                
                String versionText = versionLabel.getValue().toString();
                log.debug("Found version text: {}", versionText);
                
                // Extract version number using regex pattern for version like "2025.1.1.1"
                if (versionText != null && versionText.contains("IntelliJ IDEA")) {
                    Pattern versionPattern = Pattern.compile("IntelliJ IDEA (\\d+\\.\\d+\\.\\d+\\.\\d+)");
                    Matcher versionMatcher = versionPattern.matcher(versionText);
                    
                    if (versionMatcher.find()) {
                        String version = versionMatcher.group(1);
                        log.debug("Extracted version number: {}", version);
                        return version;
                    } else {
                        log.debug("No version pattern found, returning full text: {}", versionText);
                        return versionText.trim();
                    }
                }
                
                // Fallback: try to find in JEditorPane if JLabel approach fails
                var borderLayoutPanel = remoteRobot.find(CommonContainerFixture.class,
                        byXpath("//div[@class='MyDialog']//div[@class='BorderLayoutPanel'][.//div[@class='Box']]"),
                        DEFAULT_TIMEOUT);
                
                List<JLabelFixture> labels = borderLayoutPanel.findAll(JLabelFixture.class,
                        byXpath("//div[@class='JBLabel']//div[@class='JEditorPane']"));

                if (!labels.isEmpty()) {
                    var fullText = labels.get(1).getValue().toString();
                    log.debug("Full text from About dialog: {}", fullText);
                    
                    // Extract build number using regex: starts with # and ends with space or comma
                    Pattern pattern = Pattern.compile("#([^\\s,]+)");
                    Matcher matcher = pattern.matcher(fullText);
                    
                    if (matcher.find()) {
                        String buildNumber = "#" + matcher.group(1);
                        log.debug("Extracted build number: {}", buildNumber);
                        return buildNumber;
                    } else {
                        log.debug("No build number pattern found in: {}", fullText);
                        return fullText;
                    }
                }
                
                return null;
            }
        } catch (Exception e) {
            log.debug("Failed to extract version from About dialog", e);
            return null;
        }
    }

    /**
     * Close About dialog
     */
    private static void closeAboutDialog(RemoteRobot remoteRobot) {
        try {
            if(remoteRobot.isMac()) {
                remoteRobot.find(CommonContainerFixture.class, byXpath("//div[@class='SouthPanel'][.//div[@text='Copy and Close']]//div[@text='Close']")).click();
                remoteRobot.find(CommonContainerFixture.class, byXpath("//div[@class='JBBox'][.//div[@class='R']]//div[@class='JButton']")).click();
            } else {
            // Try to close About dialog
                try {
                    remoteRobot.find(JButtonFixture.class, byXpath("//div[@text='Close']"), Duration.ofSeconds(5)).click();
                } catch (Exception e) {
                    // Try escape or click outside
                    try {
                        remoteRobot.find(ComponentFixture.class, byXpath("//div[@accessiblename='Close']"),
                                Duration.ofSeconds(5)).click();
                    } catch (Exception closeEx) {
                        log.debug("Could not find close button, dialog may close automatically", closeEx);
                    }
                }
                TimeUtil.doDelay(500);
            }
        } catch (Exception e) {
            log.debug("Failed to close About dialog", e);
        }
    }
}