package com.cienet.idetest.test.inline;

import com.cienet.idetest.test.BaseTest;
import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.util.CommonUtil;
import com.cienet.idetest.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;

@Slf4j
public class BaseInlineTest extends BaseTest {

    protected WebElement triggerInlineChat(boolean waitGcaPrompt) {
        UiUtil.clearNotificatons(driver);
        TimeUtil.doDelay(1000);
        focusEditorPane();

        if (waitGcaPrompt) {
            // Need wait until the "Press Cmd+I to ask Gemini..." appear
            for (int attempt = 0; attempt < 3; attempt++) {
                try {
                    UiUtil.findElement(shortWait, "//*[contains(@class,'empty-editor-hint')]");
                    break;
                } catch (Exception e) {
                    log.warn("Failed to find by css");
                }
                log.warn("'Press Cmd+I to ask Gemini' not found. attempt: {}", attempt);
            }
        }

        TimeUtil.doDelay(2000);
        CharSequence ctrlKey = CommonUtil.getCtrlKey();
        actions.keyDown(ctrlKey).sendKeys("i").keyUp(ctrlKey).perform();
        By xpath = By.xpath("//*[contains(@class,'quick-input-widget show-file-icons')] " +
                "//*[contains(@class,'quick-input-and-message')] //input");
        WebElement element = driver.findElement(xpath);
        log.debug("element: {}", element);
        TimeUtil.doDelay(3000);
        Assertions.assertNotNull(element);
        Assertions.assertTrue(element.isDisplayed());
        return element;
    }

    protected WebElement focusEditorPane() {
        WebElement editorPane = UiUtil.findEditorPane(wait);
        for (int attempt = 0; attempt < 3; attempt++) {
            try {
                editorPane.click();
                break;
            } catch (Exception e) {
                // Re-find the element
                log.debug("Retry click editor pane attemp: {}", attempt);
                editorPane = UiUtil.findEditorPane(shortWait);
            }
        }
        return editorPane;
    }
}
