package com.cienet.idetest.test.generation;

import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.test.ui.ChatWindow;
import com.cienet.idetest.test.ui.LeftManuBar;
import com.cienet.idetest.util.TestExceptionHandler;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;

import java.io.IOException;
import java.util.List;

import static com.cienet.idetest.util.UiUtil.findElement;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_286_Test extends BaseGenerationTest {

    @Test
    public void test() throws IOException {
        UiUtil.openResourceProjectForTest(driver, wait, "backup_script_c.py");

        // Given
        String testSetUpString = "setUp";
        LeftManuBar leftManuBar = new LeftManuBar(driver, wait);

        // When
        ChatWindow chatWindow = leftManuBar.openGeminiChat();
        chatWindow.stepInto();
        chatWindow.checkIfProjectIsElectedAndSelectIfNot();
        chatWindow.stepOut();

        List<WebElement> viewLineElements = driver.findElements(By.className("view-line"));
        viewLineElements.get(0).click();
        actions.keyDown(Keys.COMMAND).sendKeys("a").keyUp(Keys.COMMAND).perform();
        WebElement bulbWidget = findElement(wait, By.xpath("//*[contains(@class, 'lightbulb')]"));
        bulbWidget.click();
        actions.sendKeys(Keys.ARROW_DOWN).perform();
        actions.sendKeys(Keys.ARROW_DOWN).perform();
        actions.sendKeys(Keys.ENTER).perform();
        chatWindow.stepInto();
        WebElement chatHistory = chatWindow.waitUntilChatFinish();

        // Then
        assertTrue(chatHistory.getText().contains(testSetUpString));
    }
}
