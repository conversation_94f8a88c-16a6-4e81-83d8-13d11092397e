# PowerShell script to run Java application with debugging enabled

#$jarPath = "build/libs/ide-test-automation-0.0.1-SNAPSHOT.jar"
#$debugOptions = "-Xdebug -Xrunjdwp:transport=dt_socket,address=*:8666,server=y,suspend=n"

#java $debugOptions -jar $jarPath

java -Xdebug -Xrunjdwp:transport=dt_socket,address=*:8666,server=y,suspend=n `
  "-Dwebdriver.chrome.driver=C:\Users\<USER>\chromedriver-win64\chromedriver.exe" `
  "-Duitest.test.plan.resource=testhome\test-plan-resource" `
  "-Duitest.google.project.id=aemon-projects-dev-019" `
  -jar build/libs/ide-test-automation-0.0.1-SNAPSHOT.jar