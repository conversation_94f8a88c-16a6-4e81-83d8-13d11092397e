package com.cienet.idetest.test.ui;

import com.cienet.idetest.util.UiUtil;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.WebDriverWait;

import static com.cienet.idetest.util.UiUtil.findElement;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
public class DiffView {

    private WebDriver driver;

    private WebDriverWait wait;

    private String fileName;

    private static String XPATH_EDITOR_BASE = "//*[contains(@class,'file-icon')] " +
            "//*[contains(@class,'tab-label')] //*[text()='%s']";

    public DiffView(WebDriver driver, WebDriverWait wait, String fileName) {
        this.driver = driver;
        this.wait = wait;
        this.fileName = fileName;
        log.debug("DiffView constructed - fileName: {}", fileName);
    }

    public WebElement getSourcePane() {
        // Find the source code in the left
        String sourcePaneXpath = XPATH_EDITOR_BASE.formatted(fileName);
        WebElement sourcePane = UiUtil.findElement(wait, sourcePaneXpath);
        return sourcePane;
    }

    public WebElement getSuggestedPane() {
        // Find the suggested source code in the right
        String suggestedXpath = XPATH_EDITOR_BASE.formatted(fileName + " (Gemini Diff)");
        WebElement suggestedPane = UiUtil.findElement(wait, suggestedXpath);
        return suggestedPane;
    }

}
