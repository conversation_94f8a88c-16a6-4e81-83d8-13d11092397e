package com.cienet.idetest.test.ui;

import com.cienet.idetest.util.CommonUtil;
import com.cienet.idetest.util.TimeUtil;
import com.cienet.idetest.util.UiUtil;
import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.*;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.support.ui.WebDriverWait;

import java.io.IOException;
import java.time.Duration;
import java.util.List;

import static com.cienet.idetest.util.UiUtil.findElement;

/**
 * This class is for handling the popup suggest text for Code Generation and Code Completion.
 */
@Slf4j
public class SuggestPreview {

    private static final String CONTAINS_CLASS_GHOST_TEXT = "//*[contains(@class,'ghost-text-decoration')]";

    private static final String CONTAINS_CLASS_GHOST_TEXT_PARENT = CONTAINS_CLASS_GHOST_TEXT + "/..";

    private WebDriver driver;

    private WebDriverWait wait;

    private Actions actions;

    public SuggestPreview(WebDriver driver, WebDriverWait wait) {
        this.driver = driver;
        this.wait = wait;
        this.actions = new Actions(driver);
    }

    public SuggestPreview(WebDriver driver, WebDriverWait wait, Actions actions) {
        this.driver = driver;
        this.wait = wait;
        this.actions = actions;
    }

    public String triggerSuggest(String filePath, String comment, String funcDeclare) throws IOException {
        return triggerSuggest(filePath, comment, funcDeclare, true);
    }

    /**
     * @param filePath
     * @param comment
     * @param funcDeclare
     * @return Prompt text in editor area.
     * @throws IOException
     */
    public String triggerSuggest(String filePath, String comment, String funcDeclare, boolean clearAllText) throws IOException {
        log.debug("doComtriggerSuggest() - filePath={}, prompt={}, funcDeclare={}, clearAllText",
                filePath, comment, funcDeclare, clearAllText);

        // Open the file
        if (filePath != null) {
            UiUtil.openResourceProjectForTest(driver, wait, filePath);
        }

        EditorArea editorArea = new EditorArea(driver, wait, actions);
        WebElement editAreaElem = editorArea.getEditorArea();
        Keys ctrlKey = CommonUtil.getCtrlKey();

        if (clearAllText) {
            // Enter comment
            editorArea.clearEditorText();
        } else {
            // Move to the end of the file, and add a new line.
            actions.keyDown(ctrlKey).sendKeys("a").keyUp(ctrlKey).perform();
            TimeUtil.doDelay(300);
            actions.sendKeys(Keys.ARROW_RIGHT, Keys.ENTER).perform();
            TimeUtil.doDelay(300);
            actions.sendKeys(Keys.ENTER).perform();
            TimeUtil.doDelay(300);
            actions.keyDown(ctrlKey).sendKeys(Keys.ARROW_LEFT).keyUp(ctrlKey).perform();
            TimeUtil.doDelay(300);
        }

        UiUtil.typeText(actions, comment);
        TimeUtil.doDelay(150);
        actions.sendKeys(Keys.ENTER).perform();
        String promptText = editAreaElem.getText();
        int promptLength = promptText.length();
        log.debug("promptLength: {}, promptText: {}", promptLength, promptText);
        TimeUtil.doDelay(150);
        actions.sendKeys(Keys.ENTER).perform();
        TimeUtil.doDelayInSeconds(2);

        // Try to get suggested code.
        // If it doesn't exist type the chars in funcDeclare to trigger it
        WebElement element = getCurrentLineElementWithTimeout(3000);

        if (element == null) {
            for (int i = 0; i < funcDeclare.length(); i++) {
                log.debug("Retry triggering suggest i={}", i);
                actions.sendKeys(String.valueOf(funcDeclare.charAt(i))).perform();
                element = getCurrentLineElementWithTimeout(3000);
                if (element != null) {
                    break;
                }
            }
        }

        // Get element again to avoid StaleElementReferenceException
        element = getCurrentLineElementWithTimeout(3000);
        log.debug("Got suggest code: {}", element.getText());
        return promptText;
    }

    public String getCurrentLineSuggestText() {
        List<WebElement> elements = UiUtil.findElements(wait, CONTAINS_CLASS_GHOST_TEXT);
        StringBuilder sb = new StringBuilder();
        for (WebElement element : elements) {
            sb.append(element.getText());
        }
        return sb.toString();
    }

    // This method won't throw exception
    public WebElement getCurrentLineElementWithTimeout(int timeoutMillis) {
        WebDriverWait myWait = new WebDriverWait(driver, Duration.ofMillis(timeoutMillis));
        WebElement element = null;
        try {
            element = UiUtil.findElement(myWait, CONTAINS_CLASS_GHOST_TEXT_PARENT);
        } catch (TimeoutException e) {
            log.warn("Failed to get current line element in preview. timeout={}", timeoutMillis);
        }
        return element;
    }

    public WebElement getCurrentLineElement() {
        return UiUtil.findElement(wait, CONTAINS_CLASS_GHOST_TEXT_PARENT);
    }

    public String getCurrentLine() {
        StaleElementReferenceException e = null;

        for (int i = 0; i < 3; i++) {
            try {
                WebElement currentLine = getCurrentLineElement();
                String currentLineText = currentLine.getText();
                log.debug("currentLineText: {}", currentLineText);
                return currentLineText;
            } catch (StaleElementReferenceException se) {
                log.warn("Got StaleElementReferenceException! Will retry... i={}", i);
                e = se;
            }
        }

        throw e;
    }

    // If the suggested code only has 1 line, there is no preview element.
    // Therefore, we don't wait here.
    public WebElement getPreviewElement() {
        // return UiUtil.findElement(wait, "//*[contains(@class,'suggest-preview-text')]");
        List<WebElement> elements = driver.findElements(By.xpath("//*[contains(@class,'suggest-preview-text')]"));
        if (elements != null && elements.size() > 0) {
            return elements.getFirst();
        }
        return null;
    }

    // Sometimes only 1 line is suggested,
    // so return an empty String when no preview element found.
    public String getPreviewCode() {
        StaleElementReferenceException e = null;

        for (int i = 0; i < 3; i++) {
            try {
                String previewCode = "";
                WebElement previewEle = getPreviewElement();
                if (previewEle != null) {
                    previewCode = previewEle.getText();
                }
                log.debug("previewCode: {}", previewCode);
                return previewCode;
            } catch (StaleElementReferenceException se) {
                log.warn("Got StaleElementReferenceException! Will retry... i={}", i);
                e = se;
            }
        }

        throw e;
    }

    public String getSuggestCode() {
        String suggestCode = getCurrentLine() + "\n" + getPreviewCode();
        log.debug("suggestCode: {}", suggestCode);
        return suggestCode;
    }

    public WebElement getAcceptLabel() {
        return findElement(wait, "//a[contains(@class,'inlineSuggestionStatusBarItemLabel')]");
    }

    public WebElement getAcceptWord() {
        return findElement(wait, "//*[contains(text(),'Accept Word')]");
    }

}
