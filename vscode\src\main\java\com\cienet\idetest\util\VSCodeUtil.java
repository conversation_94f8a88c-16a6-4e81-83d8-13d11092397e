package com.cienet.idetest.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import com.cienet.idetest.vo.TestConstant;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Base64;

@Slf4j
public class VSCodeUtil {

    private static final String USER_DATA_DIR = CommonUtil.getCurrentDir() + "/.vscodeProfile";

    public static WebDriver getChromeDriver() {
        // Setup ChromeDriver using WebDriver Manager
        String sysProperty = System.getProperty("webdriver.chrome.driver");
        if (StringUtils.isEmpty(sysProperty)) {
            // use default path
            String driverPath = CommonUtil.getCurrentDir() + "/src/main/resources/chromedriver";
            System.setProperty("webdriver.chrome.driver", driverPath);
            TestConstant.WEBDRIVER_CHROME_DRIVER_PATH = driverPath;
        }

        // Set options for WebDriver, assuming VS Code or similar application
        ChromeOptions options = new ChromeOptions();
        String vscodePath = System.getProperty("vscode.path");
        CommonUtil.OsTypeEnum osType = CommonUtil.getOsTyppe();

        if (vscodePath == null) {
            vscodePath = switch (osType) {
                case Mac -> "/Applications/Visual Studio Code.app/Contents/MacOS/Electron";
                case Windows -> System.getProperty("user.home") +
                        "\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe";
                case Linux -> "/usr/share/code/code";
                default -> throw new RuntimeException("Unsupported OS type: " + osType);
            };
        }

        createUserSettingJson();

        log.debug("osType: {}, vscodePath: {}, userDataDir: {}", osType, vscodePath, USER_DATA_DIR);
        options.setBinary(vscodePath);
        options.addArguments("--user-data-dir=" + USER_DATA_DIR);

        WebDriver driver = new ChromeDriver(options);
        SessionUtil.setDriver(driver);
        return driver;
    }

    private static void createUserSettingJson() {
        String fileContent = "{\n  \"geminicodeassist.project\": \"" + TestConstant.UITEST_GOOGLE_PROJECT_ID + "\"\n}\n";
        createConfigJson("settings.json", fileContent);
    }

    public static void createConfigJson(String fileName, String fileCotent) {
        File userDataDir = new File(USER_DATA_DIR + "/User");
        if (!userDataDir.exists()) {
            if (userDataDir.mkdirs()) {
                log.debug("Directory created: {}", USER_DATA_DIR);
            } else {
                throw new RuntimeException("Failed to create directory: " + USER_DATA_DIR);
            }
        }

        File settingsFile = new File(userDataDir, fileName);
        try {
            if (settingsFile.exists()) {
                log.warn("The directory {} already exists. It will be removed.", userDataDir);
                removeUserSettingDir();
            }
            if (settingsFile.createNewFile()) {
                log.debug("File created: {}", settingsFile.getAbsolutePath());
            } else {
                throw new RuntimeException("File already exists: " + settingsFile.getAbsolutePath());
            }

            try (FileWriter writer = new FileWriter(settingsFile)) {
                writer.write(fileCotent);
                log.debug("Content written to settings.json.");
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private static void removeUserSettingDir() {
        File userDataDir = new File(USER_DATA_DIR);
        log.debug("Delete all contents in {}", USER_DATA_DIR);
        if (userDataDir.exists()) {
            try {
                deleteDirectoryRecursively(userDataDir);
                log.debug("User settings directory removed: {}", USER_DATA_DIR);
            } catch (IOException e) {
                log.error("Failed to remove user settings directory: {}", USER_DATA_DIR, e);
            }
        } else {
            log.debug("User settings directory does not exist: {}", USER_DATA_DIR);
        }
    }

    private static void deleteDirectoryRecursively(File directory) throws IOException {
        File userDataDir = new File(USER_DATA_DIR);
        log.trace("Delete the directory {}", directory);
        File[] files = directory.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    deleteDirectoryRecursively(file);
                } else {
                    if (!file.delete()) {
                        throw new IOException("Failed to delete file: " + file.getAbsolutePath());
                    }
                }
            }
        }
        if (!directory.delete()) {
            throw new IOException("Failed to delete directory: " + directory.getAbsolutePath());
        }
    }

    public static void closeDriver(WebDriver driver) {
        log.debug("closeDriver() entered.");
        if (driver != null) {
            driver.quit();
        }
        removeUserSettingDir();
        SessionUtil.deleteAll();
    }

    public static void doScreenShot() {
        doScreenShot(SessionUtil.getTestName());
    }

    public static void doScreenShot(String testName) {
        WebDriver driver = SessionUtil.getDriver();
        if (driver == null) {
            log.warn("Skip doScreenShot() due to driver is null.");
            return;
        }

        // do a screenshot, file path will like this:
        // srcFile path: /var/folders/rc/k770f3cn42d28b514s6sgzvc0000gn/T/screenshot9944157027389088325.png
        // destFile path: {logPath}/{resultOid}_{testName}_{yyyyMMdd_HHmmss}_screenshot.png
        String logPath = TestConstant.UITEST_SCREENSHOT_PATH;
        File srcFile = ((TakesScreenshot) driver).getScreenshotAs(OutputType.FILE);
        File destFile = new File(getScreenShotPath(testName, logPath));
        log.warn("Save screenshot to: {}", destFile.getAbsolutePath());
        try {
            CommonUtil.copyFileWithFlush(srcFile, destFile);
        } catch(IOException e) {
            // ignore
            log.error("doScreenShot failed. testName={}, srcFile={}, destFile={}", testName, srcFile, destFile);
        }
    }

    private static String getScreenShotPath(String testName, String logPath) {
        return String.format("%s/%s_%s_screenshot.png",
                logPath, SessionUtil.getResultOid(), testName);
    }

    public static String getScreenShotBase64(String testName) {
        File destFile = new File(getScreenShotPath(testName, TestConstant.UITEST_SCREENSHOT_PATH));

        if (!destFile.exists()) {
            log.error("Screenshot file does not exist: {}", destFile.getAbsolutePath());
            return null;
        }

        try {
            byte[] fileContent = Files.readAllBytes(destFile.toPath());
            return Base64.getUrlEncoder().encodeToString(fileContent);
        } catch (IOException e) {
            log.error("Failed to read screenshot file to Base64: {}, exception: {}", destFile.getAbsolutePath(), e);
            return null;
        }
    }
}
