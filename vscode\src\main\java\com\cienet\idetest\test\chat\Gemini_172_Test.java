package com.cienet.idetest.test.chat;

import com.cienet.idetest.test.ui.LeftManuBar;
import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.test.ui.ChatWindow;
import com.cienet.idetest.test.ui.RightClickContextMenu;
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import static com.cienet.idetest.util.UiUtil.selectLines;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
@ExtendWith(TestExceptionHandler.class)
public class Gemini_172_Test extends BaseChatTest {

    /**
     * Gemini-172 : Explain This w/ selection. - Version1
     * "Explain This" code from selection.
     *
     * Reviewed with test team on 17/10/2024
     * Test compared with TesLink on: 16/10/2024
     * @throws InterruptedException
     */
    @Test
    void testExplain() throws IOException {
        // Given
        UiUtil.openResourceProjectForTest(driver, wait, "Chat/two_sum.py");
        int fromLineNum = 2, toLineNum = 9;

        // When
        selectLines(actions, fromLineNum, toLineNum);
        List<WebElement> viewLineElements = driver.findElements(By.className("view-line"));
        List<String> selectLines = viewLineElements.stream().map(WebElement::getText).collect(Collectors.toList());
        WebElement lineToExplain = viewLineElements.get(fromLineNum);
        LeftManuBar leftManuBar = new LeftManuBar(driver, wait);
        ChatWindow chatWindow = leftManuBar.openGeminiChat();
        chatWindow.stepInto();
        WebElement chatInputField = chatWindow.getChatInputField();
        chatInputField.sendKeys("Explain this");
        chatWindow.getSubmitButton().click();
        WebElement chatResponse = chatWindow.waitUntilChatFinish();
        String respText = chatResponse.getText();
        List<WebElement> chatMsgBoxes = chatWindow.findChatMsgBox();

        // Then
        assertTrue(!chatMsgBoxes.isEmpty() && chatMsgBoxes.size() >=2, "There should be at least 2 chat message boxes in Chat window.");

        String selectCode = selectLines.stream().collect(Collectors.joining("\n"));
        String lowerCaseResp = respText.toLowerCase();
        log.debug("Selected code=\n{}\n\nChat response=\n{}\n", selectCode, respText);

        // Check if Gemini replied: Sorry I can't help you with that.
        assertFalse(lowerCaseResp.contains("can't") && lowerCaseResp.length() <= 100,
                "Response contains \"can't\" keyword. ");
        assertTrue(respText.length() >= 500, "Gemini's response is less then 500 chars!");
    }

}