package com.cienet.idetest.test.uiaction;

import com.cienet.idetest.util.TimeUtil;
// import org.junit.jupiter.api.AfterEach; // Removed
// import org.junit.jupiter.api.BeforeEach; // Removed
import org.junit.jupiter.api.Test;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
// import org.openqa.selenium.WebDriver; // Removed
import org.openqa.selenium.WebElement;
// import org.openqa.selenium.interactions.Actions; // Removed
// import org.openqa.selenium.support.ui.WebDriverWait; // Removed

// import java.io.IOException; // Removed
// import java.time.Duration; // Removed

import static com.cienet.idetest.util.UiUtil.findElement;
// import static com.cienet.idetest.util.UiUtil.prepareTestWithOpenProject; // Removed
import static org.junit.jupiter.api.Assertions.assertFalse;

import com.cienet.idetest.test.uiaction.BaseUiActionTest;

public class Gemini_53_Test extends BaseUiActionTest {

    // Fields driver, wait, actions are inherited from BaseUiActionTest
    // setup() method is inherited from BaseUiActionTest

    private static final String DEFAULT_PROJECT = "\"trusted-testers\""; // This constant is used by the test logic

    @Test
    public void test() {
        // Given
        setProject(DEFAULT_PROJECT);
        String oldVersion = getVersion();

        // When
        setProject("\"Dogfood-user\"");
        String newVersion = getVersion();

        // Then
        assertFalse(newVersion.equals(oldVersion));
    }

    private String getVersion() {
        WebElement extensionsButton = findElement(wait, By.xpath("//*[contains(@aria-label, 'Extensions')]"));
        extensionsButton.click();
        TimeUtil.doDelayInSeconds(2);
        WebElement cloudConsoleExtension = driver.findElement(By.xpath("//*[contains(@aria-label, 'Gemini Code Assist + Google Cloud Code')]"));
        cloudConsoleExtension.click();
        extensionsButton.click();
        WebElement version = findElement(wait, By.xpath("//code[@class='version']"));
        System.out.println("VERSION: " + version.getText());
        return version.getText();
    }

    private void setProject(String project) {
        actions.keyDown(Keys.LEFT_SHIFT).keyDown(Keys.COMMAND).sendKeys("p").keyUp(Keys.LEFT_SHIFT).keyUp(Keys.COMMAND).perform();
        WebElement quickMenu = findElement(wait, By.cssSelector("[aria-describedby='quickInput_message']"));
        quickMenu.sendKeys("Open User Settings (JSON)");
        actions.sendKeys(Keys.ENTER).perform();
        if (driver.getPageSource().contains(DEFAULT_PROJECT)) {
            TimeUtil.doDelayInSeconds(2);
            actions.keyDown(Keys.SHIFT).keyDown(Keys.ARROW_UP)
                .keyUp(Keys.SHIFT).keyUp(Keys.ARROW_UP)
                .keyDown(Keys.SHIFT).keyDown(Keys.ARROW_RIGHT)
                .keyUp(Keys.SHIFT).keyUp(Keys.ARROW_RIGHT).perform();
            actions.sendKeys("\"cloudcode.updateChannel\": " + project + ",").perform();
            actions.keyDown(Keys.SHIFT).keyDown(Keys.ARROW_RIGHT)
                    .keyUp(Keys.SHIFT).keyUp(Keys.ARROW_RIGHT)
                    .sendKeys(Keys.DELETE).perform();
            actions.keyDown(Keys.COMMAND).sendKeys("s").keyUp(Keys.COMMAND).perform();
            TimeUtil.doDelayInSeconds(15);
        } else {
            actions.sendKeys(Keys.ARROW_RIGHT).perform();
            actions.sendKeys("\"cloudcode.updateChannel\": " + project + ",").perform();
            actions.keyDown(Keys.COMMAND).sendKeys("s").keyUp(Keys.COMMAND).perform();
            TimeUtil.doDelayInSeconds(5);
        }
    }

    // tearDown() method is inherited from BaseTest (via BaseUiActionTest)
    // and handles driver.quit()

}
