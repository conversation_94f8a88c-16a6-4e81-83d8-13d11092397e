package org.cienet.ide_intellij_automation_test.test.codeTransform;

import static com.intellij.remoterobot.search.locators.Locators.byXpath;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.awt.Point;
import java.time.Duration;
import java.util.List;

import org.cienet.ide_intellij_automation_test.util.TestExceptionHandler;
import org.cienet.ide_intellij_automation_test.util.TimeUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.intellij.remoterobot.fixtures.ActionButtonFixture;
import com.intellij.remoterobot.fixtures.CommonContainerFixture;
import com.intellij.remoterobot.fixtures.EditorFixture;
import com.intellij.remoterobot.fixtures.JListFixture;
import com.intellij.remoterobot.fixtures.JTextFieldFixture;
import com.intellij.remoterobot.fixtures.dataExtractor.RemoteText;
import com.intellij.remoterobot.utils.Keyboard;
import java.awt.event.KeyEvent;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_579_Test extends BaseTransformTest {
    
    private static final Logger log = LoggerFactory.getLogger(Gemini_579_Test.class);
    private static final Duration DEFAULT_TIMEOUT = Duration.ofSeconds(3);

    @Test
    public void Gemini_579() {
        // Step 1: Open a Java file in the editor
        closeAllCurrentTabs();
        openMultiplesFiles("SortArray.java");
        
        EditorFixture editor = getEditor();
        Keyboard keyboard = new Keyboard(remoteRobot);

        String fileContent = editor.getText();

        moveCursorToShowFloatingToolbar(editor);

        try {
            // Step 2: Click the Gemini icon in the floating toolbar
            ActionButtonFixture geminiButton = remoteRobot.find(ActionButtonFixture.class, 
                byXpath("//div[@myicon='logo.svg']"), DEFAULT_TIMEOUT);
            geminiButton.click();

            TimeUtil.doDelay(500);

            JListFixture myList = remoteRobot.find(JListFixture.class, byXpath("//div[@class='MyList']"), DEFAULT_TIMEOUT);
            List<RemoteText> items = myList.findAllText();

            boolean foundPromptText = false;
            for (RemoteText item : items) {
                if (item.getText().contains("Open In-Editor Prompt")) {
                    foundPromptText = true;
                    break;
                }
            }
            assertTrue(foundPromptText, "Open In-Editor Prompt option not found");
        } catch (Exception e) {
            log.error("Failed to click Gemini icon: {}", e.getMessage());
            throw e;
        }
    }

    private EditorFixture getEditor() {
        EditorFixture editor = remoteRobot.find(EditorFixture.class, byXpath("//div[@class='EditorComponentImpl']"),
            DEFAULT_TIMEOUT);
        editor.click();
        return editor;
    }

    private void moveCursorToShowFloatingToolbar(EditorFixture editor) {
        Keyboard keyboard = new Keyboard(remoteRobot);
        // Move cursor to the beginning of the file
        keyboard.hotKey(KeyEvent.VK_CONTROL, KeyEvent.VK_HOME);

        // Select all content in the file
        keyboard.hotKey(KeyEvent.VK_CONTROL, KeyEvent.VK_A);
        TimeUtil.doDelay(100);

        // Hover mouse at the first line to show floating toolbar
        Point firstLinePosition = new Point(100, 25);
        editor.moveMouse(firstLinePosition);
        TimeUtil.doDelay(500);
    }
}
