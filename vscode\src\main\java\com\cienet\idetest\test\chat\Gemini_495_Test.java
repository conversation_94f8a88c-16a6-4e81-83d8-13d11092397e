package com.cienet.idetest.test.chat;

import com.cienet.idetest.test.ui.ChatWindow;
import com.cienet.idetest.test.ui.LeftManuBar;
import com.cienet.idetest.util.RobotUtil;
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
import com.cienet.idetest.util.UiUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.WebElement;

import java.io.IOException;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@ExtendWith(TestExceptionHandler.class)
public class Gemini_495_Test extends BaseChatTest {

    /**
     * Gemini-495 : @File - Search and mark multi files
     * User can search for and select multiple files when typing a chat prompt.
     */
    @Test
    void testMultiFiles() throws IOException {
        RobotUtil.clickFullScreenKey();
        UiUtil.openResourceProjectForTest(driver, wait);
        LeftManuBar leftManuBar = new LeftManuBar(driver, wait);
        ChatWindow chatWindow = leftManuBar.openGeminiChat();
        chatWindow.stepInto();
        WebElement chatInput = chatWindow.getChatInputField();
        String placeholderText = chatInput.getAttribute("placeholder");
        log.debug("Chat input placeHolder: {}", placeholderText);
        assertTrue(placeholderText.contains("Ask Gemini or type '@'"));
        chatInput.sendKeys("@");
        TimeUtil.doDelay(1000);
        List<WebElement> fileNameList = chatWindow.getFileNameList();
        log.debug("fileNameList: {}", fileNameList);
        assertNotNull(fileNameList, "File names list should not be null");
        assertFalse(fileNameList.isEmpty(), "File names list should not be empty");

        String fnameTwoSum = "two_sum.py";
        String fnameSpaceShip = "Spaceship_ch.java";
        String fnameJobSch = "job_scheduling_ch.go";

        // Select two_sum.py
        chatInput.sendKeys("two_sum");
        TimeUtil.doDelay(1000);
        WebElement fileItem = chatWindow.getFileItem(fnameTwoSum);
        fileItem.click();
        TimeUtil.doDelay(1000);

        // Select Spaceship_ch.java
        chatInput.sendKeys("@Spaceship");
        TimeUtil.doDelay(1000);
        fileItem = chatWindow.getFileItem(fnameSpaceShip);
        fileItem.click();
        TimeUtil.doDelay(1000);

        // Select job_scheduling_ch.go
        chatInput.sendKeys("@job_scheduling");
        TimeUtil.doDelay(1000);
        fileItem = chatWindow.getFileItem(fnameJobSch);
        fileItem.click();
        TimeUtil.doDelay(1000);
        chatWindow.getSubmitButton().click();
        WebElement chatHistory = chatWindow.waitUntilChatFinish();
        List<WebElement> msgBoxes = chatWindow.findChatMsgBox();
        String chatPrompt = msgBoxes.get(0).getText();
        log.debug("chatPrompt: {}", chatPrompt);

        assertTrue(chatPrompt.contains(fnameTwoSum) && chatPrompt.contains(fnameSpaceShip)
                && chatPrompt.contains(fnameJobSch), "File names are not mentioned in chat prompt!");

        WebElement ctxItemBtn = chatWindow.getContextItemsButton();
        ctxItemBtn.click();
        TimeUtil.doDelay(1000);
        WebElement elementTwoSum = chatWindow.getContextItem(fnameTwoSum);
        log.debug("elementTwoSum: {}", elementTwoSum);
        WebElement elementSpaceShip = chatWindow.getContextItem(fnameSpaceShip);
        log.debug("elementSpaceShip: {}", elementSpaceShip);
        WebElement elementJobSch = chatWindow.getContextItem(fnameJobSch);
        log.debug("elementJobSch: {}", elementJobSch);
    }
}