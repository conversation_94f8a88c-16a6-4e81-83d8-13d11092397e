package com.cienet.idetest.test.ui;

import com.cienet.idetest.util.TimeUtil;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.support.ui.WebDriverWait;

import static com.cienet.idetest.util.UiUtil.findElement;

public class BottomBar {

    private WebDriver driver;

    private WebDriverWait wait;

    public BottomBar(WebDriver driver, WebDriverWait wait) {
        this.driver = driver;
        this.wait = wait;
    }

    public PopUpWindow googleConsoleStatus() {
        findElement(wait, By.xpath("//*[@id='googlecloudtools.cloudcode' and contains(@class, 'left')]")).click();
        return new PopUpWindow(driver, wait);
    }

    public ChatWindow openChatWindow() {
        findElement(wait, By.xpath("//*[@id='google.geminicodeassist' and contains(@class, 'right')]")).click();
        TimeUtil.doDelayInSeconds(3); // wait for the gemini chat panel to show up
        return new ChatWindow(driver, wait);
    }

    public PopUpWindow openPopUpFromStarIcon() {
        findElement(wait, By.xpath("//*[@id='googlecloudtools.cloudcode' and contains(@class, 'right')]")).click();
        return new PopUpWindow(driver, wait);
    }
}
