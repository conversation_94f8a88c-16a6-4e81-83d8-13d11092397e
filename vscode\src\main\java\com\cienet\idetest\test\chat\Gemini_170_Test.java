package com.cienet.idetest.test.chat;

import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.test.ui.ChatWindow;
import com.cienet.idetest.test.ui.LeftManuBar;
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.interactions.Actions;

import java.io.IOException;

import static com.cienet.idetest.util.UiUtil.findElement;
import static com.cienet.idetest.util.UiUtil.findElementPresence;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_170_Test extends BaseChatTest {

    private static String REMOVE_EMPTY_LINE_REGEXP = "(?m)^[ \t]*\r?\n";

    /**
     * Gemini-170 : Copy code from chat - [VSC] - Version2
     * Button to copy code block to clipboard works
     *
     * Test compared with TesLink on: 3/10/2024
     * Reviewed with test team on 16/10/2024
     * @throws IOException
     */
    @Test
    public void test() throws IOException {
        // Given
        UiUtil.openResourceProjectForTest(driver, wait, "Chat/empty.py");

        // When
        LeftManuBar leftManuBar = new LeftManuBar(driver, wait);
        ChatWindow chatWindow = leftManuBar.openGeminiChat();
        chatWindow.stepInto();
        chatWindow.checkIfProjectIsElectedAndSelectIfNot();
        chatWindow.closeTipsIfVisible();

        WebElement chatInputField = chatWindow.getChatInputField();
        chatInputField.sendKeys("write a function to add two numbers and return the result");
        chatWindow.getSubmitButton().click();

        WebElement chatHistory = chatWindow.waitUntilChatFinish();
        TimeUtil.doDelayInSeconds(3);
        assertTrue(chatHistory.isDisplayed());
        String chatHistoryText = chatHistory.getText();

        WebElement copyCode = findElement(wait, By.xpath("//*[@aria-label='copy']"));
        copyCode.click();
        // verify a pop-up notification with "Copied to clipboard" and "dismiss" button
        findElement(wait, By.xpath("//div[contains(text(),'Copied to clipboard')]"));
        findElement(wait, By.xpath("//span[@class='mdc-button__label'][contains(text(),'Dismiss')]"));
        chatWindow.stepOut();

        WebElement editorWindow = findElementPresence(wait, By.xpath("//*[contains(@class,'editor-scrollable')]"));
        editorWindow.click();
        Actions actions = new Actions(driver);
        actions.keyDown(Keys.COMMAND).sendKeys("v").keyUp(Keys.COMMAND).perform();
        TimeUtil.doDelayInSeconds(1);
        String editorWindowText = editorWindow.getText();

        // Then
        String textInfo = String.format("editorWindowText=\n[%s] \nchatHistoryText=\n[%s]", editorWindowText, chatHistoryText);
        assertTrue(chatHistoryText.replaceAll(REMOVE_EMPTY_LINE_REGEXP, "").contains(editorWindowText),
                "Copy-Paste code is different from the code in the chat history.\n" + textInfo);
    }

}