package org.cienet.ide_intellij_automation_test.logging;

import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.encoder.PatternLayoutEncoder;
import ch.qos.logback.core.FileAppender;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * DebugLogFilter individual test log files
 * Simple approach using programmatic logback configuration
 */
public class DebugLogFilter {

    private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");
    private static final String LOG_FILE_NAME_PATTERN = "%s_%s.log";

    private static final ThreadLocal<String> logPathThreadLocal = new ThreadLocal<>();

    private static final ThreadLocal<FileAppender<?>> logWritterThreadLocal = new ThreadLocal<>();

    /**
     * Start logging for a specific test
     * 
     * @param testName The name of the test
     */
    public static void startTestLogging(String testName) {

        long threadId = Thread.currentThread().getId();
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMATTER);
        String currentPath = System.getProperty("user.dir");

        // Generate log file name with thread ID for uniqueness
        String logFileName = String.format(LOG_FILE_NAME_PATTERN, testName, timestamp);
        String logPath = currentPath + "/logs/" + logFileName;
        System.out.println("startTestLogging thread id: " + Thread.currentThread().getId());
        try {

            // Remove old log files for this test
            removeOldLogFiles(testName, currentPath);

            // Ensure logs directory exists
            Path logDir = Paths.get(currentPath, "logs");
            if (!Files.exists(logDir)) {
                Files.createDirectories(logDir);
            }

            // Create a new FileAppender for this test
            LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();
            FileAppender<ch.qos.logback.classic.spi.ILoggingEvent> fileAppender = new FileAppender<>();
            fileAppender.setContext(context);
            fileAppender.setName("TestAppender_" + testName);
            fileAppender.setFile(logPath);

            // Create encoder
            PatternLayoutEncoder encoder = new PatternLayoutEncoder();
            encoder.setContext(context);
            encoder.setPattern("%d{yyyy-MM-dd HH:mm:ss} %-5level [%thread] %logger{36} - %msg%n");
            encoder.start();

            fileAppender.setEncoder(encoder);
            fileAppender.start();

            // Get the root logger and add our appender
            Logger rootLogger = context.getLogger(Logger.ROOT_LOGGER_NAME);
            rootLogger.addAppender(fileAppender);

            // Store thread-local info
            logPathThreadLocal.set(logPath);
            logWritterThreadLocal.set(fileAppender);

            System.out.println(
                    "Started logging for test: " + testName + " on thread: " + threadId + " to file: " + logPath);

        } catch (Exception e) {
            System.err.println("Failed to start logging for test: " + testName + " on thread: " + threadId);
            e.printStackTrace();
        }
    }

    /**
     * Stop logging for current test and cleanup resources
     */
    public static void stopTestLogging() {
        var writer = logWritterThreadLocal.get();
        if (writer == null) {
            System.out.println("No writer found for current thread");
            return;
        }

        try {
            // Remove appender from root logger
            LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();
            Logger rootLogger = context.getLogger(Logger.ROOT_LOGGER_NAME);

            rootLogger.detachAppender(writer.getName());
            writer.stop();
            System.out.println("Stopped logging for test");

        } catch (Exception e) {
            System.err.println("Failed to stop logging for test");
            e.printStackTrace();
        } finally {
            logWritterThreadLocal.remove();
        }
    }

    /**
     * Get test log content from current test's log file
     * 
     * @return String content of the log file
     */
    public static String getTestLogContent() {
        Path logFilePath = Paths.get(logPathThreadLocal.get());
        StringBuilder logContent = new StringBuilder();

        try (Stream<String> lines = Files.lines(logFilePath)) {
            for (String line : lines.collect(Collectors.toList())) {
                logContent.append(line).append(System.lineSeparator());
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return logContent.toString();
    }

    /**
     * Remove old log files for the given test name
     */
    private static void removeOldLogFiles(String testName, String currentPath) throws IOException {
        Path logDir = Paths.get(currentPath, "logs");
        if (!Files.exists(logDir)) {
            return;
        }

        Files.list(logDir)
        .filter(path -> path.getFileName().toString().matches(testName + "_\\d{8}_\\d{6}\\.log"))
        .forEach(path -> {
            try {
                Files.delete(path);
            } catch (Exception e) {
                e.printStackTrace(); // Handle the exception appropriately
            }
        });
    }

    /**
     * Get current log path
     */
    public static String getCurrentLogPath() {
        return logPathThreadLocal.get();
    }
}