package com.cienet.ide.test.common.util;

import lombok.extern.slf4j.Slf4j;
import org.monte.media.av.Format;
import org.monte.media.av.Registry;
import org.monte.media.screenrecorder.ScreenRecorder;

import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.util.Date;

@Slf4j
public class AgentScreenRecorder extends ScreenRecorder {

    private String filename;

    public AgentScreenRecorder(GraphicsConfiguration cfg, Rectangle captureArea, Format fileFormat, Format screenFormat,
            Format mouseFormat, Format audioFormat, File movieFolder, String filename)
            throws IOException, AWTException {
        super(cfg, captureArea, fileFormat, screenFormat, mouseFormat, audioFormat, movieFolder);
        this.filename = filename;
    }

    @Override
    protected File createMovieFile(Format fileFormat) throws IOException {
        if (!movieFolder.exists()) {
            movieFolder.mkdirs();
        } else if (!movieFolder.isDirectory()) {
            throw new IOException("\"" + movieFolder + "\" is not a directory.");
        }
        File videoFile = new File(movieFolder, filename + "_" + TimeUtil.dateToY13s(new Date()) + "." + Registry.getInstance()
                .getExtension(fileFormat));
        log.debug("createMovieFile - videoFile: {}", videoFile);
        return videoFile;
    }

}
