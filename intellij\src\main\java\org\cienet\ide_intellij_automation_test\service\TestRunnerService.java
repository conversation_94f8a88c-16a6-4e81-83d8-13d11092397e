package org.cienet.ide_intellij_automation_test.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.cienet.ide_intellij_automation_test.logging.DebugLogFilter;
import org.cienet.ide_intellij_automation_test.test.BaseTest;
import org.cienet.ide_intellij_automation_test.util.IntellijUtil;
import org.cienet.ide_intellij_automation_test.util.SessionUtil;
import com.cienet.ide.test.common.vo.TestSummary;
import com.cienet.ide.test.common.vo.VersionInfo;
import org.junit.platform.engine.discovery.DiscoverySelectors;
import org.junit.platform.launcher.Launcher;
import org.junit.platform.launcher.LauncherDiscoveryRequest;
import org.junit.platform.launcher.core.LauncherDiscoveryRequestBuilder;
import org.junit.platform.launcher.core.LauncherFactory;
import org.junit.platform.launcher.listeners.SummaryGeneratingListener;
import org.junit.platform.launcher.listeners.TestExecutionSummary;
import org.reflections.Reflections;
import org.reflections.scanners.Scanners;
import org.springframework.stereotype.Service;
import jakarta.annotation.PostConstruct;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.ArrayList;

@Service
@Slf4j
public class TestRunnerService {

    public static String TEST_PACKAGE = "org.cienet.ide_intellij_automation_test";

    private Reflections reflections = null;

    private Map<String, Method> testMethodMap = null;

    @PostConstruct
    public void init() {
        log.info("Initializing TestRunnerService...");
        System.setProperty("java.awt.headless", "false");
        reflections = new Reflections(TEST_PACKAGE, Scanners.MethodsAnnotated);
        log.info("Reflections initialized with package: {}", TEST_PACKAGE);
        
        // Map method name -> method (not class name -> method)
        testMethodMap = reflections.getMethodsAnnotatedWith(org.junit.jupiter.api.Test.class).stream().collect(
                Collectors.toMap(Method::getName, method -> method));
        
        log.info("Found {} test methods: {}", testMethodMap.size(), testMethodMap.keySet());
    }

    public TestSummary runTestByTestName(String testName) {
        TestExecutionSummary summary = null;
        long startTime = System.currentTimeMillis();
        log.info("runTestByTestName() enter...testName={}, resultOid={}, startTime={}",
                testName, SessionUtil.getResultOid(), startTime);
        
        // Look up method by name directly (not by class name)
        Method method = testMethodMap.get(testName);
        log.debug("Looking for method: {}, found: {}", testName, method);

        if (method == null) {
            log.error("Test method not found! testName={}, available methods: {}", 
                    testName, testMethodMap.keySet());
            throw new RuntimeException("Test method not found! " + testName);
        }

        try {
            Class<?> testClass = method.getDeclaringClass();
            log.info("Running test method: {} from class: {}", method.getName(), testClass.getSimpleName());
            
            LauncherDiscoveryRequest request = LauncherDiscoveryRequestBuilder.request()
                    .selectors(DiscoverySelectors.selectMethod(testClass, method)).build();

            Launcher launcher = LauncherFactory.create();
            SummaryGeneratingListener listener = new SummaryGeneratingListener();
            launcher.registerTestExecutionListeners(listener);
            launcher.execute(request);

            summary = listener.getSummary();
        } catch (Throwable e) {
            log.error("Failed to run testcase: ", e);
            throw new RuntimeException(e);
        }

        return toTestSummary(summary, testName, startTime);
    }

    public List<String> getAvailableTestCases() {
        // Return actual method names, not class names
        return testMethodMap.keySet().stream()
                .sorted()
                .collect(Collectors.toList());
    }

    private TestSummary toTestSummary(TestExecutionSummary summary, String testName, long startTime) {
        String exceptionMessage = "";
        if (StringUtils.isEmpty(exceptionMessage) && summary.getTestsFailedCount() > 0) {
            exceptionMessage = summary.getFailures().get(0).getException().getMessage();
        }

        log.info("Test Execution Summary: " + "\n" +
                "======================" + "\n" +
                "Tests started: " + testName + "\n" +
                "Tests successful: " + summary.getTestsSucceededCount() + "\n" +
                "Tests failed: " + summary.getTestsFailedCount() + "\n" +
                "Tests aborted: " + summary.getTestsAbortedCount() + "\n" +
                "Tests skipped: " + summary.getTestsSkippedCount() + "\n" +
                "Execution time: " + (summary.getTimeFinished() - summary.getTimeStarted()) + "\n" +
                "======================");

        // Get agent log content - this is the critical part for agentLog functionality
        String agentLog = "";
        log.info("Main thread id: {}", Thread.currentThread().getId());
        try {
            agentLog = DebugLogFilter.getTestLogContent();
            log.info("Successfully retrieved agent log with {} characters for test: {}", 
                    agentLog.length(), testName);
        } catch (Exception e) {
            log.error("Failed to get agent log content for test: {}", testName, e);
            agentLog = "Error retrieving log: " + e.getMessage();
        }

        // Determine test result using enum like VSCode
        TestSummary.TestResult testResult = null;

        if (summary.getTestsSucceededCount() == 1) {
            testResult = TestSummary.TestResult.Successful;
        } else if (summary.getTestsAbortedCount() == 1) {
            testResult = TestSummary.TestResult.Aborted;
        } else {
            testResult = TestSummary.TestResult.Failed;
        }

        // Create TestSummary using setter methods like VSCode (not builder pattern)
        TestSummary testSummary = new TestSummary();
        testSummary.setTestName(testName);
        
        // Get version info from BaseTest
        VersionInfo versionInfo = BaseTest.getVersionInfo();
        if (versionInfo != null) {
            testSummary.setIdeVersion(versionInfo.ideVersion());
            testSummary.setPluginVersion(versionInfo.pluginVersion());
        } else {
            log.warn("No versionInfo found!");
            testSummary.setIdeVersion("");
            testSummary.setPluginVersion("");
        }

        testSummary.setTestResult(testResult);
        testSummary.setExecutionTime(summary.getTimeFinished() - summary.getTimeStarted());
        testSummary.setAgentLog(agentLog);
        testSummary.setExceptionMessage(exceptionMessage);

        if (testResult != TestSummary.TestResult.Successful) {
            try {
                testSummary.setScreenShot(IntellijUtil.getScreenShotBase64());
            } catch (Exception e) {
                log.error("Failed to capture screenshot for test: {}", testName, e);
                testSummary.setScreenShot(null);
            }
        }

        log.info("Test summary created successfully for test: {} with agentLog size: {} chars", 
                testName, agentLog.length());

        return testSummary;
    }

    public List<TestSummary> runAllTests() {
        List<String> testNames = getAvailableTestCases();
        log.info("Found {} test cases to run", testNames.size());
        List<TestSummary> results = new ArrayList<>();
        for (String testName : testNames) {
            log.info("Running test: {}", testName);
            TestSummary summary = runTestByTestName(testName);
            results.add(summary);
        }

        return results;
    }
}
