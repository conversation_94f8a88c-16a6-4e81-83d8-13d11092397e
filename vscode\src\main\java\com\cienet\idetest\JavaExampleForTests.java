package com.cienet.idetest;

import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.stream.Collectors;

// this is a sample program to demonstrate what a multi-file 
// unit test prompt file should look like
public class JavaExampleForTests {

    private Map<String, Map<String, String>> map = new HashMap<>();

    public JavaExampleForTests() {
    }

    public JavaExampleForTests(Map<String, Map<String, String>> map) {
        this.map = map;
    }

    public void addValue(String key, Map<String, String> value) {
        map.put(key, value);
    }

    public Map<String, Map<String, String>> getMap() {
        return map;
    }

    public Map<String, String> flatten() {
        return map.values().stream()
                .flatMap(m -> m.entrySet().stream())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    public List<String> flattenToList() {
        return map.values().stream()
                .flatMap(m -> m.entrySet().stream())
                .map(Map.Entry::getValue)
                .collect(Collectors.toList());
    }

    public static void main(String[] args) {
        Map<String, Map<String, String>> map = new HashMap<>();
        Map<String, String> map1 = new HashMap<>();
        map1.put("key1", "value1");
        map1.put("key2", "value2");
        map1.put("key3", "value3");
        Map<String, String> map2 = new HashMap<>();
        map2.put("key4", "value4");
        map2.put("key5", "value5");
        map2.put("key6", "value6");
        map.put("map1", map1);
        map.put("map2", map2);

        JavaExampleForTests myMap = new JavaExampleForTests(map);
        System.out.println(myMap.getMap());
    }
}
