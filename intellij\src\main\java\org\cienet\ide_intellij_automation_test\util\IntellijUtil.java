package org.cienet.ide_intellij_automation_test.util;

import com.intellij.remoterobot.RemoteRobot;
import com.redhat.devtools.intellij.commonuitest.UITestRunner;

import lombok.extern.slf4j.Slf4j;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.time.Duration;
import java.util.Base64;
import javax.imageio.ImageIO;
import com.intellij.remoterobot.fixtures.EditorFixture;
import static com.intellij.remoterobot.search.locators.Locators.byXpath;

@Slf4j
public class IntellijUtil {

    public static String getScreenShotBase64() {
        RemoteRobot remoteRobot = UITestRunner.getRemoteRobot();
        if (remoteRobot == null) {
            log.warn("RemoteRobot is null, cannot take screenshot");
            return "";
        }

        try {
            BufferedImage screenshot = remoteRobot.getScreenshot();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(screenshot, "PNG", outputStream);
            return Base64.getUrlEncoder().encodeToString(outputStream.toByteArray());
        } catch (Exception e) {
            log.error("Failed to take screenshot: ", e);
            return "";
        }
    }

    public static void waitShowCodeSuggestion(RemoteRobot remoteRobot, long seconds) {
        remoteRobot.find(
              EditorFixture.class,
              byXpath("//div[contains(@visible_text, 'visibleText') and @class='EditorComponentImpl']".replace(
                      "visibleText",
                      "|| to complete")),
              Duration.ofSeconds(seconds));
      }
} 