import org.jetbrains.intellij.platform.gradle.IntelliJPlatformType

plugins {
    id("org.jetbrains.intellij.platform") version ("2.2.1")
}

repositories {
    mavenCentral()
    intellijPlatform {
        defaultRepositories()
    }
    gradlePluginPortal() // Ensure the plugin can be resolved
}

//val platformVersion = System.getenv("IDEA_VERSION")
//    ?: providers.gradleProperty("ideaVersion").get()

val platformVersion: String = providers.gradleProperty("platformVersion").get()

dependencies {
    intellijPlatform {
        create(IntelliJPlatformType.IntellijIdeaUltimate, platformVersion)
        // create(IntelliJPlatformType.IntellijIdeaCommunity, platformVersion)
    }
}

// https://plugins.jetbrains.com/docs/intellij/tools-intellij-platform-gradle-plugin-tasks.html#runIdeForUiTests
val runIdeForUiTests by intellijPlatformTesting.runIde.registering {
    task {
        jvmArgumentProviders += CommandLineArgumentProvider {
            listOf(
                "-Dide.mac.message.dialogs.as.sheets=false",
                "-Djb.privacy.policy.text=<!--999.999-->",
                "-Djb.consents.confirmation.enabled=false",
                "-Dide.mac.file.chooser.native=false",
                "-DjbScreenMenuBar.enabled=false",
                "-Dapple.laf.useScreenMenuBar=false",
                "-Didea.trust.all.projects=true",
                "-Dide.show.tips.on.startup.default.value=false"
            )
        }
    }
    plugins {
        robotServerPlugin()
    }
}
