package com.cienet.idetest.test.completion;

import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;

import java.io.IOException;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertTrue;

@Deprecated // This case has been deactivated in TestLink
@ExtendWith(TestExceptionHandler.class)
public class Gemini_9_Test extends BaseCompletionTest {

    @Test
    public void test() throws IOException {
        // Given
        UiUtil.openResourceProjectForTest(driver, wait, "Chat/empty.py");
        clearEditorText();

        // When
        actions.sendKeys("for file in os.listdir(src_folder):").perform();
        actions.sendKeys(Keys.ENTER).perform();
        TimeUtil.doDelay(1000);
        actions.sendKeys(Keys.TAB).perform();
        TimeUtil.doDelay(1000);
        List<WebElement> codeLines = driver.findElements(By.className("view-line"));

        // Then
        assertTrue(codeLines.size() >= 2);
        assertTrue(codeLines.stream().anyMatch(line -> line.getText().contains("os.")));

        // Given
        clearEditorText();

        // When
        actions.sendKeys("for file in os.listdir(src_folder):").perform();
        actions.sendKeys(Keys.ENTER).perform();
        TimeUtil.doDelay(1000);
        actions.keyDown(Keys.COMMAND).keyDown(Keys.ARROW_RIGHT).perform();
        List<WebElement> codeLinesPart2 = driver.findElements(By.className("view-line"));

        // Then
        assertTrue(codeLinesPart2.size() >= 2);
        assertTrue(codeLinesPart2.stream().anyMatch(line -> line.getText().contains("os.")));
    }

}