package com.cienet.idetest.test.inline;

import com.cienet.idetest.test.ui.DiffView;
import com.cienet.idetest.test.ui.TelemetryPanel;
import com.cienet.idetest.util.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_375_Test extends BaseInlineTest {

    @Test
    public void testInlineGenerate() throws IOException {
        assertTrue(UiUtil.checkTestMode(wait));
        UiUtil.openResourceProjectForTest(driver, wait);
        UiUtil.openNewFile(actions);
        RobotUtil.clickFullScreenKey();

        TimeUtil.doDelay(1000);
        UiUtil.clearNotificatons(driver);
        TimeUtil.doDelay(1000);

        WebElement inlineChat = triggerInlineChat(true);
        inlineChat.sendKeys("/generate a flask app");
        TimeUtil.doDelay(2000);
        RobotUtil.clickEnterKey();
        TimeUtil.doDelay(5000);
        DiffView diffView = new DiffView(driver, wait, "Untitled-1");

        // Find the source code in the left
        WebElement sourcePane = diffView.getSourcePane();
        assertTrue(sourcePane.isDisplayed());

        // Find the suggested source code in the right
        WebElement suggestedPane = diffView.getSuggestedPane();
        assertTrue(suggestedPane.isDisplayed());
    }

}
