package com.cienet.idetest.test.chat;

import com.cienet.idetest.util.*;
import com.cienet.idetest.test.ui.ChatWindow;
import com.cienet.idetest.test.ui.LeftManuBar;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebElement;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
@ExtendWith(TestExceptionHandler.class)
public class Gemini_266_Test extends BaseChatTest {


    /**
     * Gemini-266 : Generate Unit Test via chat message. - Version2
     * To verify Generate Unit test function can be trigger via chat message and work well.
     *
     * Reviewed with test team on 17/10/2024
     * Test compared with TesLink on: 17/10/2024
     * @throws IOException
     */
    @Test
    void testGenUnitTest() throws IOException {
        // Given
        String expectedMessagePart = "import unittest";
        UiUtil.openResourceProjectForTest(driver, wait, "Chat/two_sum.py");

        // When
        LeftManuBar leftManuBar = new LeftManuBar(driver, wait);
        ChatWindow chatWindow = leftManuBar.openGeminiChat();
        chatWindow.stepInto();
        String prompt = "Generate unit test";

        try {
            WebElement chatInputField = chatWindow.getChatInputField();
            chatInputField.sendKeys(prompt);
            chatWindow.getSubmitButton().click();
        } catch (Exception e) {
            log.warn("Failed to find chatInputField", e);
            // Try to enter text directly
            RobotUtil.enterText(prompt);
            TimeUtil.doDelay(200);
            RobotUtil.clickEnterKey();
        }

        WebElement chatHistory = chatWindow.waitUntilChatFinish();
        String respText = chatHistory.getText();

        // Then
        log.debug("Generate unit test result: {}", respText);
        assertTrue(respText.contains(expectedMessagePart),
                String.format("Response does not contain '%s' keyword", expectedMessagePart));
        assertFalse(respText.length() < 100 && respText.contains("I can't"),
                "Response contains \"I can't\" keyword. ");
    }

    private String getChatResponseByJs() {
        String respText;
        log.info("Try to get it from javascript.");
        JavascriptExecutor js = (JavascriptExecutor) driver;
        String script = "var element = document.evaluate(arguments[0], document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue; " +
                "if (element) { return element.innerText || element.textContent; } else { return ''; }";
        respText = (String) js.executeScript(script, "//div[contains(@class, 'chat-history-item system')]");
        assertTrue(StringUtils.isNoneEmpty(respText), "Failed to get response text from javascript.");
        return respText;
    }

}
