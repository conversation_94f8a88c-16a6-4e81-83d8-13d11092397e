package com.cienet.ide.test.common.util;

import lombok.extern.slf4j.Slf4j;
import org.monte.media.av.Format;
import org.monte.media.math.Rational;
import org.monte.media.screenrecorder.ScreenRecorder;
import org.monte.media.av.FormatKeys.MediaType;

import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;

import static org.monte.media.av.FormatKeys.EncodingKey;
import static org.monte.media.av.FormatKeys.FrameRateKey;
import static org.monte.media.av.FormatKeys.KeyFrameIntervalKey;
import static org.monte.media.av.FormatKeys.MIME_AVI;
import static org.monte.media.av.FormatKeys.MediaTypeKey;
import static org.monte.media.av.FormatKeys.MimeTypeKey;
import static org.monte.media.av.codec.video.VideoFormatKeys.CompressorNameKey;
import static org.monte.media.av.codec.video.VideoFormatKeys.DepthKey;
import static org.monte.media.av.codec.video.VideoFormatKeys.ENCODING_AVI_TECHSMITH_SCREEN_CAPTURE;
import static org.monte.media.av.codec.video.VideoFormatKeys.QualityKey;

@Slf4j
public class ScreenRecordHelper {

    private ScreenRecorder recorder = null;

    private static final String VIDEOS = "videos";

    private static Integer maxVideoFiles = null;

    private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");

    public void startRecord(String testCaseName, String resultOid) throws Exception {
        log.debug("enter startRecord()...");
        GraphicsConfiguration gc = GraphicsEnvironment.getLocalGraphicsEnvironment().getDefaultScreenDevice()
                .getDefaultConfiguration();

        // Remove the tailing _Test from the testCaseName
        if (testCaseName.indexOf("_Test") > 0) {
            testCaseName = testCaseName.substring(0, testCaseName.indexOf("_Test"));
        }

        String fileName = testCaseName + "_" + resultOid;
        log.debug("startRecord() - fileName: {}", fileName);
        recorder = new AgentScreenRecorder(gc, null,
                new Format(MediaTypeKey, MediaType.FILE, MimeTypeKey, MIME_AVI),
                new Format(MediaTypeKey, MediaType.VIDEO, EncodingKey, ENCODING_AVI_TECHSMITH_SCREEN_CAPTURE,
                        CompressorNameKey, ENCODING_AVI_TECHSMITH_SCREEN_CAPTURE, DepthKey, (int) 24, FrameRateKey,
                        Rational.valueOf(15), QualityKey, 1.0f, KeyFrameIntervalKey, (int) (15 * 60)),
                new Format(MediaTypeKey, MediaType.VIDEO, EncodingKey, "black", FrameRateKey, Rational.valueOf(30)),
                null,
                new File(VIDEOS),
                fileName);
        deleteOldFiles(testCaseName);
        recorder.start();
    }

    private void deleteOldFiles(String testCaseName) {
        File movieFolder = new File(VIDEOS);
        File[] files = movieFolder.listFiles();
        if (files == null) {
            return;
        }
        // Keep 5 latest videos and delete the old files starts with testCaseName.
        File[] matchingFiles = movieFolder.listFiles((dir, name) -> name.startsWith(testCaseName));
        int maxFiles = getMaxVideoFiles();

        if (matchingFiles != null && matchingFiles.length >= maxFiles) {
            Arrays.sort(matchingFiles, (f1, f2) -> Long.compare(f2.lastModified(), f1.lastModified()));
            for (int i = maxFiles - 1; i < matchingFiles.length; i++) {
                if (matchingFiles[i].delete()) {
                    log.info("Deleted old video file: {}", matchingFiles[i].getName());
                } else {
                    log.warn("Failed to delete old video file: {}", matchingFiles[i].getName());
                }
            }
        }
    }

    private int getMaxVideoFiles() {
        if (maxVideoFiles != null) {
            return maxVideoFiles;
        }
        String maxVideoFilesVal = System.getProperty("uitest.max.video.files");
        if (maxVideoFilesVal != null) {
            maxVideoFiles = Integer.parseInt(maxVideoFilesVal);
        } else {
            maxVideoFiles = 5;
        }
        log.info("maxVideoFiles: {}", maxVideoFiles);
        return maxVideoFiles;
    }

    public void stopRecord() throws IOException {
        log.debug("stop startRecord()...");
        recorder.stop();
    }
}
