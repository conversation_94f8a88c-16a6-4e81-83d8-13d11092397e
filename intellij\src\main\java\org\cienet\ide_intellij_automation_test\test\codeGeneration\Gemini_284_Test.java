package org.cienet.ide_intellij_automation_test.test.codeGeneration;

import org.cienet.ide_intellij_automation_test.util.TimeUtil;
import org.cienet.ide_intellij_automation_test.util.CommonUtil;
import org.cienet.ide_intellij_automation_test.util.TestExceptionHandler;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.cienet.ide_intellij_automation_test.util.IntellijUtil;

import com.intellij.remoterobot.RemoteRobot;
import com.intellij.remoterobot.fixtures.EditorFixture;
import com.intellij.remoterobot.fixtures.CommonContainerFixture;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import static com.intellij.remoterobot.search.locators.Locators.byXpath;
import java.time.Duration;

import com.intellij.remoterobot.utils.Keyboard;
import java.awt.event.KeyEvent;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_284_Test extends BaseGenerationTest {
    @Test
    void Gemini_284() throws IOException {
        openEmptyFileWithMultiplesFiles();
        
        String prompt = "# Write a function to check if a number is prime or not. \n";
        
        EditorFixture editor = CommonUtil.prepareEditor(remoteRobot);
        Keyboard keyboard = new Keyboard(remoteRobot);
        
        CommonUtil.enterTextByLines(keyboard, prompt);
        keyboard.hotKey(KeyEvent.VK_ENTER);
        
        String originalCode = editor.getText();
        TimeUtil.doDelay(1000);

        // When - Trigger Code Generation
        CommonUtil.triggerCodeGeneration(remoteRobot, keyboard, 2000);
        
        // Check if suggestion is available and get initial state
        String origPreviewCode = getSuggestedCode(editor);

        int origCodeLength = origPreviewCode.length();
        
        // Accept a line in suggested code using hotkey
        acceptLineByHotkey(keyboard);
        TimeUtil.doDelay(2000);
        
        // Get state after accepting line
        String newPreviewCode = getSuggestedCode(editor);
        
        String finalCode = editor.getText();

        assertTrue(remoteRobot.find(
                EditorFixture.class,
                byXpath("//div[contains(@visible_text, 'to complete')]"),
                Duration.ofSeconds(10)).hasText("to complete"),
                "Code generation did not complete successfully. Expected 'to complete' in the editor.");
        
        assertTrue(finalCode.length() - originalCode.length() > 5,
                "Accept Line function seems not working. Final code should be significantly longer. Final code=" + finalCode);
        
        TimeUtil.doDelay(1000);
    }
    
    private void acceptLineByHotkey(Keyboard keyboard) {
        if (remoteRobot.isMac()) {
            keyboard.hotKey(KeyEvent.VK_META, KeyEvent.VK_RIGHT);
        } else {
            keyboard.hotKey(KeyEvent.VK_END);
        }
    }
    
    private String getSuggestedCode(EditorFixture editor) {
        try {
            // Get current editor text which should include suggestions
            return editor.getText();
        } catch (Exception e) {
            return "";
        }
    }
}
