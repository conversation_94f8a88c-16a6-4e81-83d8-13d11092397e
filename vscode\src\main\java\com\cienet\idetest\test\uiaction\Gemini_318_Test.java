package com.cienet.idetest.test.uiaction;

// import com.cienet.idetest.util.CommonUtil; // Removed
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
// import org.junit.jupiter.api.AfterEach; // Removed
// import org.junit.jupiter.api.BeforeEach; // Removed
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
// import org.openqa.selenium.WebDriver; // Removed
import org.openqa.selenium.WebElement;
// import org.openqa.selenium.chrome.ChromeDriver; // Removed
// import org.openqa.selenium.chrome.ChromeOptions; // Removed
// import org.openqa.selenium.support.ui.WebDriverWait; // Removed

// import java.time.Duration; // Removed

// import static com.cienet.idetest.util.UiUtil.checkRestrictedModeAndAccept; // Removed
import static com.cienet.idetest.util.UiUtil.findElement;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.cienet.idetest.test.uiaction.BaseUiActionTest;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_318_Test extends BaseUiActionTest {

    // Fields driver, wait are inherited from BaseUiActionTest
    // setup() method is inherited from BaseUiActionTest

    @Test
    public void test() throws InterruptedException {
        // When
        WebElement extensionsButton = findElement(wait, By.className("codicon-extensions-view-icon"));
        extensionsButton.click();
        TimeUtil.doDelayInSeconds(4);
        WebElement cloudConsoleExtension = driver.findElement(By.xpath("//*[contains(@aria-label, 'Gemini Code Assist + Google Cloud Code')]"));
        cloudConsoleExtension.click();
        WebElement disableButton = driver.findElement(By.cssSelector("[aria-label='Disable this extension']"));
        disableButton.click();
        WebElement restartExtensionButton = driver.findElement(By.cssSelector("[aria-label='Please restart extensions to disable this extension.']"));
        restartExtensionButton.click();

        // Then
        Exception exception = assertThrows(Exception.class, () -> {
            findElement(wait, By.cssSelector("[aria-label='Gemini Code Assist: Smart Actions']"));
        });
        assertTrue(exception.getMessage().contains("Expected condition failed: waiting for visibility of element located by By.cssSelector"));

        WebElement enableButton = driver.findElement(By.cssSelector("[aria-label='Enable this extension']"));
        enableButton.click();
        assertTrue(findElement(wait, By.cssSelector("[aria-label='Gemini Code Assist']")).isDisplayed());
        assertTrue(findElement(wait, By.cssSelector("[aria-label='material-spark-off']")).isDisplayed());
    }

    // tearDown() method is inherited from BaseTest (via BaseUiActionTest)
    // and handles driver.quit()
}
