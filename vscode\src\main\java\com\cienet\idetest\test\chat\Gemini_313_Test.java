package com.cienet.idetest.test.chat;

import com.cienet.idetest.test.ui.BottomBar;
import com.cienet.idetest.test.ui.ChatWindow;
import com.cienet.idetest.test.ui.PopUpWindow;
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;

import static com.cienet.idetest.util.UiUtil.findElement;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_313_Test extends BaseChatTest {

    /**
     * Gemini-313 : Thumbs up and down in Gemini chat for quick response quality feedback - Version4
     * To validate thumbs up/down function in chat windows.
     *
     * Test compared with TesLink on: 3/10/2024
     */
    @Test
    void test() {
        // Given
        BottomBar bottomBar = new BottomBar(driver, wait);
        String chatJunitMessagePart = "@Test";

        // When
        PopUpWindow popUpWindow = bottomBar.googleConsoleStatus();
        popUpWindow.selectGoogleCloudProject();

        ChatWindow chatWindow = bottomBar.openChatWindow();

        chatWindow.stepInto();
        chatWindow.closeTipsIfVisible();
        WebElement chatInputField = chatWindow.getChatInputField();
        chatInputField.sendKeys("generate example java unit test");
        chatWindow.getSubmitButton().click();
        WebElement chatHistory = chatWindow.waitUntilChatFinish();
        WebElement thumbsUpIcon = findElement(wait, By.xpath("//*[@aria-label='Thumbs Up']"));
        WebElement thumbsDownIcon = findElement(wait, By.xpath("//*[@aria-label='Thumbs Down']"));
        TimeUtil.doDelayInSeconds(3);

        // Then
        assertTrue(chatHistory.getText().contains(chatJunitMessagePart));

        assertTrue(thumbsUpIcon.isDisplayed());
        thumbsUpIcon.click();
        WebElement helpToImproveLink1 = findElement(wait, By.xpath("//*[contains(text(),'Help us improve')]"));

        assertTrue(thumbsDownIcon.isDisplayed());
        thumbsDownIcon.click();
        WebElement helpToImproveLink2 = findElement(wait, By.xpath("//*[contains(text(),'Help us improve')]"));

        helpToImproveLink2.click();
        WebElement feedbackContainer = findElement(wait, By.cssSelector(".feedback-container"));
        String feedbackFormText = feedbackContainer.getText();
        assertTrue(feedbackContainer.isDisplayed());
        assertTrue(feedbackFormText.contains("Feedback for") && feedbackFormText.contains("Submit Feedback"));
    }

}
