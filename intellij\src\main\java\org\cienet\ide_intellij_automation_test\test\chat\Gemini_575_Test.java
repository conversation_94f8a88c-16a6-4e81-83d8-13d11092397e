package org.cienet.ide_intellij_automation_test.test.chat;

import org.cienet.ide_intellij_automation_test.util.TimeUtil;
import org.junit.jupiter.api.Test;
import java.io.IOException;
import com.intellij.remoterobot.RemoteRobot;
import com.intellij.remoterobot.fixtures.JListFixture;
import com.intellij.remoterobot.fixtures.dataExtractor.RemoteText;
import com.intellij.remoterobot.utils.Keyboard;

import java.time.Duration;
import java.awt.event.KeyEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.List;

import static com.intellij.remoterobot.search.locators.Locators.byXpath;
import static org.junit.jupiter.api.Assertions.assertTrue;


public class Gemini_575_Test extends BaseChatTest{
    private static final Logger log = LoggerFactory.getLogger(Gemini_575_Test.class);
    private static final Duration DEFAULT_TIMEOUT = Duration.ofSeconds(3);

    @Test
    protected void Gemini_575() throws IOException {
        closeAllCurrentTabs();
        openMultiplesFiles("SortArray.java");

        TimeUtil.doDelay(500);

        Keyboard keyboard = new Keyboard(remoteRobot);
        if (remoteRobot.isMac()) {
            keyboard.hotKey(KeyEvent.VK_META, KeyEvent.VK_BACK_SLASH);
        } else {
            keyboard.hotKey(KeyEvent.VK_CONTROL, KeyEvent.VK_BACK_SLASH);
        }

        try {
            boolean hasChatPromt = false;

            JListFixture chatPrompt = remoteRobot.find(JListFixture.class, byXpath("//div[@class='JList']"), DEFAULT_TIMEOUT);
            List<RemoteText> items = chatPrompt.findAllText();
            for (RemoteText item : items) {
                log.debug("item.getText()==========: '{}'", item.getText());
                if (item.getText().contains("/fix")) {
                    hasChatPromt = true;
                    break;
                }
            }
            assertTrue(hasChatPromt, "Chat Prompt should be visible");
        } catch (Exception e) {
            log.error("Chat Prompt not found: {}", e.getMessage());
            throw e;
        }
    }
}
