package com.cienet.idetest.vo;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class TestConstant {

    public static String WEBDRIVER_CHROME_DRIVER_PATH = "./src/main/resources/chromedriver";

    public static String UITEST_GOOGLE_PROJECT_ID = "ai-dev-preview-external"; // default project name

    // public static String UITEST_CANONICAL_ANSWER_PATH = "/Users/<USER>/dev/CIeNET/canonical-answer";

    public static String UITEST_TEST_PLAN_RESOURCE_PATH = "/Users/<USER>/dev/CIeNET/test-plan-resource";

    public static String UITEST_OSA_SCRIPT_PATH = "./src/main/resources";

    public static String UITEST_SCREENSHOT_PATH = "logs";

    public static String ACTION_MENU_GENERATE_CODE_LABEL = "Gemini: Generate code (ctrl+enter)";

    static {
        String sysWebDriverPath = System.getProperty("webdriver.chrome.driver");
        if (StringUtils.isNoneEmpty(sysWebDriverPath)) {
            WEBDRIVER_CHROME_DRIVER_PATH = sysWebDriverPath;
        }
        log.debug("static block...sysWebDriverPath={}, WEBDRIVER_CHROME_DRIVER_PATH={}", sysWebDriverPath, WEBDRIVER_CHROME_DRIVER_PATH);

        String sysPrjId = System.getProperty("uitest.google.project.id");
        if (StringUtils.isNoneEmpty(sysPrjId)) {
            UITEST_GOOGLE_PROJECT_ID = sysPrjId;
        }
        log.debug("static block...sysPrjId={}, UITEST_GOOGLE_PROJECT_ID={}", sysPrjId, UITEST_GOOGLE_PROJECT_ID);

        // String sysCanonicalAnswer = System.getProperty("uitest.canonical.answer");
        // if (StringUtils.isNoneEmpty(sysCanonicalAnswer)) {
        //     UITEST_CANONICAL_ANSWER_PATH = sysCanonicalAnswer;
        // }
        // log.debug("static block...sysCanonicalAnswer={}, UITEST_CANONICAL_ANSWER_PATH={}", sysCanonicalAnswer, UITEST_CANONICAL_ANSWER_PATH);

        String sysTestPlanResource = System.getProperty("uitest.test.plan.resource");
        if (StringUtils.isNoneEmpty(sysTestPlanResource)) {
            UITEST_TEST_PLAN_RESOURCE_PATH = sysTestPlanResource;
        }
        log.debug("static block...sysTestPlanResource={}, UITEST_TEST_PLAN_RESOURCE_PATH={}", sysTestPlanResource, UITEST_TEST_PLAN_RESOURCE_PATH);

        String sysOsaScript = System.getProperty("uitest.osa.script");
        if (StringUtils.isNoneEmpty(sysOsaScript)) {
            UITEST_OSA_SCRIPT_PATH = sysOsaScript;
        }
        log.debug("static block...sysOsaScript={}, UITEST_OSA_SCRIPT_PATH={}", sysOsaScript, UITEST_OSA_SCRIPT_PATH);
    }

    // get chrome driver setting from springboot properties if available
    private static String propWebDriverPath;
    @Value("${webdriver.chrome.driver}")
    public void setPropWebDriverPath(String propWebDriverPath) {
        String sysWebDriverPath = System.getProperty("webdriver.chrome.driver");
        if (StringUtils.isEmpty(sysWebDriverPath) && StringUtils.isNoneEmpty(propWebDriverPath)) {
            System.setProperty("webdriver.chrome.driver", propWebDriverPath);
            WEBDRIVER_CHROME_DRIVER_PATH = propWebDriverPath;
        }
        log.debug("setPropWebDriverPath() end...sysWebDriverPath={}, propWebDriverPath={}, WEBDRIVER_CHROME_DRIVER_PATH={}",
                sysWebDriverPath, propWebDriverPath, WEBDRIVER_CHROME_DRIVER_PATH);
    }

    // get projId from springboot application properties
    private static String propPrjId;
    @Value("${uitest.google.project.id}")
    public void setPropPrjId(String propPrjId) {
        String sysPrjId = System.getProperty("uitest.google.project.id");
        if (StringUtils.isEmpty(sysPrjId) && StringUtils.isNoneEmpty(propPrjId)) {
            System.setProperty("uitest.google.project.id", propPrjId);
            UITEST_GOOGLE_PROJECT_ID = propPrjId;
        }
        log.debug("setPropPrjId() end...sysPrjId={}, propPrjId={}, UITEST_GOOGLE_PROJECT_ID={}",
                sysPrjId, propPrjId, UITEST_GOOGLE_PROJECT_ID);
    }

    // private static String propCanonicalAnswer;
    // @Value("${uitest.canonical.answer}")
    // public void setPropCanonicalAnswer(String propCanonicalAnswer) {
    //     String sysCanonicalAnswer = System.getProperty("uitest.canonical.answer");
    //     if (StringUtils.isEmpty(sysCanonicalAnswer) && StringUtils.isNoneEmpty(propCanonicalAnswer)) {
    //         System.setProperty("uitest.canonical.answer", propCanonicalAnswer);
    //         UITEST_CANONICAL_ANSWER_PATH = propCanonicalAnswer;
    //     }
    //     log.debug("setPropCanonicalAnswer() end...sysCanonicalAnswer={}, propCanonicalAnswer={}, UITEST_CANONICAL_ANSWER_PATH={}",
    //             sysCanonicalAnswer, propCanonicalAnswer, UITEST_CANONICAL_ANSWER_PATH);
    // }

    private static String propTestPlanResource;
    @Value("${uitest.test.plan.resource}")
    public void setPropTestPlanResource(String propTestPlanResource) {
        String sysTestPlanResource = System.getProperty("uitest.test.plan.resource");
        if (StringUtils.isEmpty(sysTestPlanResource) && StringUtils.isNoneEmpty(propTestPlanResource)) {
            System.setProperty("uitest.test.plan.resource", propTestPlanResource);
            UITEST_TEST_PLAN_RESOURCE_PATH = propTestPlanResource;
        }
        log.debug("setPropTestPlanResource() end...sysTestPlanResource={}, propTestPlanResource={}, UITEST_GOOGLE_PROJECT_ID={}",
                sysTestPlanResource, propTestPlanResource, UITEST_TEST_PLAN_RESOURCE_PATH);
    }

    private static String propOsaScript;
    @Value("${uitest.osa.script}")
    public void setPropOsaScript(String propOsaScript) {
        String sysOsaScript = System.getProperty("uitest.osa.script");
        if (StringUtils.isEmpty(sysOsaScript) && StringUtils.isNoneEmpty(propOsaScript)) {
            System.setProperty("uitest.osa.script", propOsaScript);
            UITEST_OSA_SCRIPT_PATH = propOsaScript;
        }
        log.debug("setPropOsaScript() end...sysOsaScript={}, propOsaScript={}, UITEST_OSA_SCRIPT_PATH={}",
                sysOsaScript, propOsaScript, UITEST_OSA_SCRIPT_PATH);
    }

    public static final long WEBDRIVER_WAIT_TIME_SHORT = 15; // 15 seconds

    public static final long WEBDRIVER_WAIT_TIME_NORMAL = 25; // 25 seconds

    public static final long WEBDRIVER_WAIT_TIME_LONG = 50; // 50 seconds

}
