package com.cienet.idetest.test.uiaction;

import com.cienet.idetest.util.CommonUtil;
import com.cienet.idetest.util.RobotUtil;
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.util.YDoToolUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;
import java.io.IOException;
import java.util.List;

import static com.cienet.idetest.util.UiUtil.findElement;
import static com.cienet.idetest.util.UiUtil.findElements;

@Slf4j
@ExtendWith(TestExceptionHandler.class)
public class Gemini_21_Test extends BaseUiActionTest {

    /**
     * A control panel/toolbar appears after hovering over a suggestion
     *
     */
    @Test
    public void test() throws IOException {
        RobotUtil.clickFullScreenKey();
        // Given
        UiUtil.openResourceProjectForTest(driver, wait, "Python/prime.py");
        clearEditorText();

        // When
        actions.sendKeys("# function to find prime numbers in an array").perform();
        actions.sendKeys(Keys.ENTER).perform();
        TimeUtil.doDelayInSeconds(1);
        actions.sendKeys("def ").perform();
        TimeUtil.doDelayInSeconds(3);
        actions.sendKeys("prime_").perform();
        TimeUtil.doDelayInSeconds(3);

        List<WebElement> lines = findElements(wait, By.className("view-line")); // find lines in editor area
        WebElement lastLine = lines.get(lines.size() - 1); // get the last line

        int xStart = lastLine.getLocation().getX();
        int yStart = lastLine.getLocation().getY();
        RobotUtil.mouseMove(xStart, yStart);
        TimeUtil.doDelayInSeconds(2);
        RobotUtil.mouseMove(xStart, yStart, xStart + 150, yStart+50, 3, 3, 100);

        // Then
        findElement(wait, By.xpath("//*[contains(text(),'Accept Word')]"));
        TimeUtil.doDelayInSeconds(2);
    }
}
