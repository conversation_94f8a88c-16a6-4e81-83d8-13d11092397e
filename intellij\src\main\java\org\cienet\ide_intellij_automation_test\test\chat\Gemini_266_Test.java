package org.cienet.ide_intellij_automation_test.test.chat;
import org.junit.jupiter.api.Test;
import java.io.IOException;
import com.intellij.remoterobot.RemoteRobot;
import com.intellij.remoterobot.fixtures.CommonContainerFixture;
import com.intellij.remoterobot.fixtures.EditorFixture;
import com.intellij.remoterobot.fixtures.JTextFieldFixture;
import java.time.Duration;

import static com.intellij.remoterobot.search.locators.Locators.byXpath;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertFalse;

public class Gemini_266_Test extends BaseChatTest{
    @Test
    protected void Gemini_266() throws IOException {
        
        //startGeminiTest();
        // Given
        String expectedMessagePart = "import unittest";
        openResourceProjectForTest("Chat/two_sum.py");

        // When
        JTextFieldFixture askGemini = remoteRobot.find(
        JTextFieldFixture.class,
        byXpath("//div[@class='JBTextArea']"),
        Duration.ofSeconds(30));
        askGemini.setText("Generate unit test");

        CommonContainerFixture submitButton = remoteRobot.find(
        CommonContainerFixture.class,
        byXpath("//div[@text='Submit']"),
        Duration.ofSeconds(20));
        submitButton.click();

        EditorFixture chatHistory = remoteRobot.find(
        EditorFixture.class,
        byXpath("//div[contains(@visible_text, 'Assuming')]"),
        Duration.ofSeconds(30));

        // Then
        assertTrue(chatHistory.getText().contains(expectedMessagePart));    
        assertFalse(chatHistory.getText().contains("can't"));
    }
}
