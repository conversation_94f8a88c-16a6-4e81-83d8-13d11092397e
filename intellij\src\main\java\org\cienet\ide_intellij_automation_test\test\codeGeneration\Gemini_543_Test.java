package org.cienet.ide_intellij_automation_test.test.codeGeneration;

import com.intellij.remoterobot.fixtures.ActionButtonFixture;
import com.intellij.remoterobot.fixtures.EditorFixture;
import com.intellij.remoterobot.fixtures.JListFixture;
import com.intellij.remoterobot.fixtures.dataExtractor.RemoteText;
import com.intellij.remoterobot.utils.Keyboard;
import org.cienet.ide_intellij_automation_test.util.CommonUtil;
import org.cienet.ide_intellij_automation_test.util.TestExceptionHandler;
import org.cienet.ide_intellij_automation_test.util.TimeUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.awt.*;
import java.awt.event.KeyEvent;
import java.io.IOException;
import java.time.Duration;

import static com.intellij.remoterobot.search.locators.Locators.byXpath;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_543_Test extends BaseGenerationTest {

    @Test
    void Gemini_543() throws IOException {
        openMultiplesFiles("Generation/QuickSort.java");
        String prompt = """
            // Given an array of integers nums, sort the array in ascending order using quick sort and return it
            """;

        EditorFixture editor = CommonUtil.prepareEditor(remoteRobot);
        Keyboard keyboard = new Keyboard(remoteRobot);

        CommonUtil.enterTextByLines(keyboard, prompt);
        int initialTextLength = editor.getText().length();

        RemoteText promptText = getPromptTextElementAndSelectIt(keyboard, editor, prompt);

        moveCursorToShowFloatingToolbar(promptText, editor);

        triggerGenerateCode(keyboard, editor);

        String generatedText = editor.getText();
        int generatedTextLength = generatedText.trim().length();

        assertTrue(
                generatedTextLength > initialTextLength,
                String.format("Code generation via floating toolbar failed. Generated text:\n%s\n", generatedText)
        );
    }

    private RemoteText getPromptTextElementAndSelectIt(Keyboard keyboard, EditorFixture editor, String prompt) {
        keyboard.key(KeyEvent.VK_HOME);
        RemoteText promptText = editor.getData().getAll().getLast();
        promptText.moveMouse();

        TimeUtil.doDelayInSeconds(1);

        editor.selectText(prompt.trim());

        TimeUtil.doDelayInSeconds(1);
        return promptText;
    }

    private void moveCursorToShowFloatingToolbar(RemoteText promptText, EditorFixture editor) {
        int pointX = (int) promptText.getPoint().getX();
        int pointY = (int) promptText.getPoint().getY();

        editor.moveMouse(new Point(pointX + 100, pointY));
    }

    private void triggerGenerateCode(Keyboard keyboard, EditorFixture editor) {
        ActionButtonFixture geminiActionButton = remoteRobot.find(
                ActionButtonFixture.class,
                byXpath("//div[@myicon='logo.svg']"),
                Duration.ofSeconds(5));

        geminiActionButton.moveMouse();

        JListFixture actionList = remoteRobot.find(
                JListFixture.class,
                byXpath("//div[@class='MyList']"),
                Duration.ofSeconds(3));

        actionList.findText("Generate Code").click();

        TimeUtil.doDelayInSeconds(5);

        keyboard.key(KeyEvent.VK_TAB);
        editor.click();
    }

}