package org.cienet.ide_intellij_automation_test.test.codeGeneration;

import com.intellij.remoterobot.fixtures.EditorFixture;
import com.intellij.remoterobot.fixtures.JLabelFixture;
import com.intellij.remoterobot.fixtures.dataExtractor.RemoteText;
import com.intellij.remoterobot.utils.Keyboard;
import org.cienet.ide_intellij_automation_test.util.CommonUtil;
import org.cienet.ide_intellij_automation_test.util.TestExceptionHandler;
import org.cienet.ide_intellij_automation_test.util.TimeUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.event.KeyEvent;
import java.io.IOException;
import java.time.Duration;

import static com.intellij.remoterobot.search.locators.Locators.byXpath;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;


@ExtendWith(TestExceptionHandler.class)
public class Gemini_47_Test extends BaseGenerationTest {

    private static final Logger log = LoggerFactory.getLogger(Gemini_47_Test.class);

    @Test
    void Gemini_47() throws IOException {
        openEmptyFileWithMultiplesFiles();
        String prompt = """
            # write a function to add two numbers and return the result
            """;

        EditorFixture editor = CommonUtil.prepareEditor(remoteRobot);
        Keyboard keyboard = new Keyboard(remoteRobot);

        CommonUtil.enterTextByLines(keyboard, prompt);

        RemoteText promptText = editor.getData().getAll().getLast();

        CommonUtil.triggerCodeGeneration(remoteRobot, keyboard, 30);

        // move cursor to prompt text to make completion dialog appear
        promptText.moveMouse();
        TimeUtil.doDelayInSeconds(3);

        boolean hasCompletion = CommonUtil.hasCompletionDialog(remoteRobot);
        log.debug("Completion dialog (before escape): " + hasCompletion);

        assertTrue(hasCompletion, "Code generation failed. No suggestion.");

        escapeCompletion(keyboard, editor);
        TimeUtil.doDelayInSeconds(3);

        hasCompletion = CommonUtil.hasCompletionDialog(remoteRobot);
        log.debug("Completion dialog (after escape): " + hasCompletion);

        assertFalse(hasCompletion, "Code generation failed. Suggestion is still shown after escape.");
    }

    private void escapeCompletion(Keyboard keyboard, EditorFixture editor) {
        keyboard.key(KeyEvent.VK_ESCAPE);
        editor.click();
    }
}