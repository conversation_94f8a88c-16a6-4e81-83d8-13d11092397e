package org.cienet.ide_intellij_automation_test.test.codeTransform;

import static org.junit.jupiter.api.Assertions.assertTrue;

import org.cienet.ide_intellij_automation_test.util.TestExceptionHandler;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import com.cienet.ide.test.common.constants.FileNameTest;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_577_Test extends BaseTransformTest {

    @Test
    public void Gemini_577() {
        openInEditorPrompt(FileNameTest.QUICK_SORT_JAVA, null);

        assertTrue(true,
                String.format("Code transform failed."));
    }
}