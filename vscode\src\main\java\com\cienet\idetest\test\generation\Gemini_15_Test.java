package com.cienet.idetest.test.generation;

import com.cienet.idetest.util.TestExceptionHandler;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.IOException;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_15_Test extends BaseGenerationTest {

    @Test
    void testCodeGeneration() throws IOException {
        testCodeGenByText("def add(a, b : int) -> int:");
    }

}
