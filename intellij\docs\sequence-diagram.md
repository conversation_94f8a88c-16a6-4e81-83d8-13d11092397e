```mermaid
sequenceDiagram
    participant User
    participant SwaggerUI
    participant API
    participant IntelliJ
    participant GCA
    participant TestFramework

    %% Initial Setup
    Note over User,TestFramework: Setup Phase

    %% Step 1: Clone & Start Application
    User->>User: git clone [repository-url]
    User->>User: cd test-gemini
    User->>User: ./gradlew bootRun
    User->>SwaggerUI: Access http://localhost:8080

    %% Step 2: Start IntelliJ IDE
    User->>SwaggerUI: Execute /tests/start
    SwaggerUI->>API: POST /tests/start
    API->>IntelliJ: Launch IDE instance
    Note over IntelliJ: Wait for 5-10 seconds
    IntelliJ-->>API: IDE Started Successfully
    API-->>SwaggerUI: 200 OK Response
    SwaggerUI-->>User: Display Success Message

    %% Step 3: Setup GCA Plugin
    User->>SwaggerUI: Execute /tests/setup
    SwaggerUI->>API: POST /tests/setup
    API->>IntelliJ: Install GCA Plugin
    IntelliJ->>GCA: Initialize Plugin
    Note over IntelliJ,GCA: Plugin Installation (30s timeout)
    GCA-->>IntelliJ: Plugin Ready
    IntelliJ-->>API: Setup Complete
    API-->>SwaggerUI: 200 OK Response
    SwaggerUI-->>User: Display Success Message

    %% Step 4: Manual Configuration Steps
    Note over User,GCA: Manual Configuration Required
    User->>IntelliJ: Open 'test-plan-resource-main' project
    User->>GCA: Login with Google Account
    User->>GCA: Select Cloud Project
    Note over User,GCA: Wait for configuration to complete

    %% Step 5: Get Test Cases
    User->>SwaggerUI: Execute /tests/getAvailableTestCases
    SwaggerUI->>API: GET /tests/getAvailableTestCases
    API->>TestFramework: Fetch Available Tests
    TestFramework-->>API: Return Test List
    API-->>SwaggerUI: Test Cases JSON Response
    SwaggerUI-->>User: Display Available Tests

    %% Step 6: Execute Test Case
    User->>SwaggerUI: Execute /tests/run/{testName}
    SwaggerUI->>API: POST /tests/run/{testName}
    API->>TestFramework: Initialize Test

    %% Step 7: Test Execution
    par Test Features
        TestFramework->>GCA: Test Code Generation
        Note over TestFramework,GCA: Using keyboard shortcuts (Alt+G)
        GCA-->>TestFramework: Generated Code Result
    and
        TestFramework->>GCA: Test Code Completion
        Note over TestFramework,GCA: Using Tab key
        GCA-->>TestFramework: Code Suggestions
    and
        TestFramework->>GCA: Test Code Explanation
        Note over TestFramework,GCA: Using right-click menu
        GCA-->>TestFramework: Code Explanation
    and
        TestFramework->>GCA: Test Code Transformation
        Note over TestFramework,GCA: Using context menu
        GCA-->>TestFramework: Transformed Code
    end

    %% Step 8: Results
    TestFramework-->>API: Test Execution Results
    API-->>SwaggerUI: Test Results JSON
    SwaggerUI-->>User: Display Test Results

    %% Limitations
    Note over User,TestFramework: Limitations:<br/>1. GCA Plugin: Timeout during loading<br/>2. Project Access: Path dependencies<br/>3. Plugin Stability: May need reinstallation<br/>4. Manual Steps: Some actions require user intervention

    %% Best Practices
    Note over User,TestFramework: Best Practices:<br/>1. Timing: Use appropriate waits<br/>2. Error Handling: Implement retries<br/>3. Maintenance: Regular updates
```
