package com.cienet.idetest.test.explanation;

import com.cienet.idetest.test.ui.ChatWindow;
import com.cienet.idetest.test.ui.LeftManuBar;
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
import com.cienet.idetest.util.UiUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.support.ui.WebDriverWait;

import java.io.IOException;
import java.time.Duration;
import java.util.List;

import static com.cienet.idetest.util.UiUtil.findElement;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_277_Test {

    private WebDriver driver;
    private WebDriverWait wait;

    private Actions actions;

    @BeforeEach
    public void setup() throws InterruptedException, IOException {
        UiUtil.openResourceProjectForTest(driver, wait, "backup_script_c.py");
        wait = new WebDriverWait(driver, Duration.ofSeconds(10));
        actions = new Actions(driver);
    }

    @Test
    public void test() {
        // Given
        String explainMessagePart ="backup_script";
        LeftManuBar leftManuBar = new LeftManuBar(driver, wait);

        // When
        ChatWindow chatWindow = leftManuBar.openGeminiChat();
        chatWindow.stepInto();
        chatWindow.checkIfProjectIsElectedAndSelectIfNot();
        chatWindow.stepOut();

        List<WebElement> viewLineElements = driver.findElements(By.className("view-line"));
        viewLineElements.get(0).click();
        actions.keyDown(Keys.COMMAND).sendKeys("a").keyUp(Keys.COMMAND).perform();
        WebElement bulbWidget = findElement(wait, By.xpath("//*[contains(@class, 'lightbulb')]"));
        bulbWidget.click();
        actions.sendKeys(Keys.ARROW_DOWN).perform();
        actions.sendKeys(Keys.ENTER).perform();
        chatWindow.stepInto();
        WebElement chatHistory = chatWindow.waitUntilChatFinish();
        TimeUtil.doDelayInSeconds(5);

        // Then
        assertTrue(chatHistory.getText().contains(explainMessagePart));
    }

    @AfterEach
    public void tearDown() {
        if (driver != null) {
            driver.quit();
        }
    }
}
