package com.cienet.idetest.test.completion;

import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.ide.test.common.vo.LanguageTypeEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.IOException;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_17_Test extends BaseCompletionTest {

    /**
     * Gemini-17
     * Basic sanity test that code completion can be triggered with Python.
     * Only tests if code completion can be triggered and returns a suggestion -
     * does not test the syntax, formatting, or quality of the suggestion.
     *
     * @throws IOException
     */
    @Test
    public void testCodeComplete() throws IOException {
        String filePath = "Code Completion/Python/sort_array.py";
        String comment = "#Given an array of integers, sort the array in ascending order using merge sort and return it";
        String funcDeclare = "def merge(left, right):";
        doCompletionTest(filePath, comment, funcDeclare);
    }

}