package com.cienet.idetest.test.uiaction;

import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.test.ui.ChatWindow;
import com.cienet.idetest.test.ui.RightClickContextMenu;
import com.cienet.idetest.util.TestExceptionHandler;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;

import java.io.IOException;
import java.util.List;

import static com.cienet.idetest.util.UiUtil.findElements;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
@ExtendWith(TestExceptionHandler.class)
public class Gemini_283_Test extends BaseUiActionTest {

    /**
     * To verify Generate Unit test function can be trigger via right click context menu and work well.
     *
     */
    @Test
    public void testGenerateUnitTest() throws IOException {
        // Given
        UiUtil.openResourceProjectForTest(driver, wait, "Chat/two_sum.py");
        List<WebElement> viewLineElements = findElements(wait, By.className("view-line")); // find lines in editor area
        WebElement firstLine = viewLineElements.get(0); // get the first line

        // When
        RightClickContextMenu rightClickContextMenu = new RightClickContextMenu(driver, wait);
        ChatWindow chatWindow = rightClickContextMenu.triggerGenUnitTestFromContextMenu(wait, firstLine);
        chatWindow.stepInto();
        WebElement chatResponse = chatWindow.waitUntilChatFinish();
        log.debug("Generate unit tests response: " + chatResponse.getText());

        // Then
        assertTrue(chatResponse.getText().contains("import unittest"));
    }
}
