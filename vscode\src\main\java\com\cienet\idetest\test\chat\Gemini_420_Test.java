package com.cienet.idetest.test.chat;

import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.test.ui.ChatWindow;
import com.cienet.idetest.test.ui.LeftManuBar;
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;

import java.io.IOException;

import static com.cienet.idetest.util.UiUtil.findElement;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
@ExtendWith(TestExceptionHandler.class)
public class Gemini_420_Test extends BaseChatTest {

    /**
     * Gemini-420 : Chat - Open suggestion code in diff view - [VSC] - Version1
     * User can open suggestion code in diff view from chat and compare the difference between original code and suggestion code.
     *
     * Test compared with TesLink on: 3/10/2024
     * @throws IOException
     */
    @Test
    public void test() throws IOException {
        // Given
        UiUtil.openResourceProjectForTest(driver, wait, "Chat/twoSum.py");

        // When
        LeftManuBar leftManuBar = new LeftManuBar(driver, wait);
        ChatWindow chatWindow = leftManuBar.openGeminiChat();
        chatWindow.stepInto();

        WebElement chatInputField = chatWindow.getChatInputField();
        chatInputField.sendKeys("Improve this code");
        chatWindow.getSubmitButton().click();

        WebElement chatHistory = chatWindow.waitUntilChatFinish();
        assertTrue(chatHistory.isDisplayed());
        assertFalse(chatHistory.getText().contains("Sorry"), "Gemini cannot improve this code.");

        // click the diff button to show diff view
        WebElement diffButton = findElement(wait, By.xpath("//*[@aria-label='diff']"));
        diffButton.click();
        chatWindow.stepOut();
        TimeUtil.doDelayInSeconds(3);

        // Then
        WebElement editor = UiUtil.getEditorWindow(wait);
        // validate diff view shows
//        findElement(wait, By.xpath("//div[@class='editor original']/descendant::div[@class='view-line']"));
//        findElement(wait, By.xpath("//div[contains(@class, 'view-lines line-delete')]/descendant::div[@class='view-line']"));
        findElement(wait, By.xpath("//div[@class='editor original']"));
        findElement(wait, By.xpath("//div[contains(@class, 'view-lines line-delete')]"));
        findElement(wait, By.xpath("//*[@aria-label='Revert Block']"));

        // hide the left side panel to get side-by-side diff view
        actions.keyDown(Keys.COMMAND).sendKeys("b").keyUp(Keys.COMMAND).perform();
        // validate side-by-side view shows
        WebElement sideEditor = findElement(wait, By.cssSelector("div[class='monaco-diff-editor side-by-side']"));
        assertTrue(sideEditor.isDisplayed());
        WebElement originEditor = findElement(wait, By.cssSelector("div[class='editor original']"));
        WebElement modifiedEditor = findElement(wait, By.cssSelector("div[class='editor modified']"));
        WebElement revertButton = findElement(wait, By.xpath("//*[@aria-label='Revert Block']"));

        assertTrue(originEditor.isDisplayed(), "Original code is not displayed");
        assertTrue(modifiedEditor.isDisplayed(), "Suggestion code is not displayed");
        assertTrue(revertButton.isDisplayed(), "Revert button is not displayed");
    }

}