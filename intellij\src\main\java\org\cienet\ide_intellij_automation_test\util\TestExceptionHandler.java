package org.cienet.ide_intellij_automation_test.util;

import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.api.extension.LifecycleMethodExecutionExceptionHandler;
import org.junit.jupiter.api.extension.TestExecutionExceptionHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.redhat.devtools.intellij.commonuitest.UITestRunner;

public class TestExceptionHandler implements LifecycleMethodExecutionExceptionHandler, TestExecutionExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger(TestExceptionHandler.class);

    @Override
    public void handleAfterAllMethodExecutionException(ExtensionContext context, Throwable throwable) throws Throwable {
        log.debug("enter handleAfterAllMethodExecutionException()...throwable={}", throwable.toString());
        handleException(context);
        LifecycleMethodExecutionExceptionHandler.super.handleAfterAllMethodExecutionException(context, throwable);
    }

    @Override
    public void handleAfterEachMethodExecutionException(ExtensionContext context, Throwable throwable)
            throws Throwable {
        log.debug("enter handleAfterEachMethodExecutionException()...throwable={}", throwable.toString());
        handleException(context);
        LifecycleMethodExecutionExceptionHandler.super.handleAfterEachMethodExecutionException(context, throwable);
    }

    @Override
    public void handleBeforeAllMethodExecutionException(ExtensionContext context, Throwable throwable)
            throws Throwable {
        log.debug("enter handleBeforeAllMethodExecutionException()...throwable={}", throwable.toString());
        handleException(context);
        LifecycleMethodExecutionExceptionHandler.super.handleBeforeAllMethodExecutionException(context, throwable);
    }

    @Override
    public void handleBeforeEachMethodExecutionException(ExtensionContext context, Throwable throwable)
            throws Throwable {
        log.debug("enter handleBeforeEachMethodExecutionException()...throwable={}", throwable.toString());
        handleException(context);
        LifecycleMethodExecutionExceptionHandler.super.handleBeforeEachMethodExecutionException(context, throwable);
    }

    @Override
    public void handleTestExecutionException(ExtensionContext context, Throwable throwable) throws Throwable {
        log.debug("enter handleTestExecutionException()...throwable={}", throwable.toString());
        handleException(context);

        throw throwable;
    }

    private void handleException(ExtensionContext context) throws Throwable {
        String testName = context.getRequiredTestMethod().getName(); // ex: Gemini_428
        log.debug("enter handleException() for {}, resultOid={}", testName, SessionUtil.getResultOid());
        log.debug("Execution exception: {}", context.getExecutionException());

        // close the IntelliJ app since tearDown() is not called when exception is
        // thrown
        UITestRunner.closeIde();
    }
}
