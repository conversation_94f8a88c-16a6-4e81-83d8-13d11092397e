package com.cienet.idetest.test.chat;

import com.cienet.idetest.test.ui.ChatWindow;
import com.cienet.idetest.test.ui.LeftManuBar;
import com.cienet.idetest.util.RobotUtil;
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
import com.cienet.idetest.util.UiUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.WebElement;

import java.io.IOException;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@ExtendWith(TestExceptionHandler.class)
public class Gemini_671_Test extends BaseChatTest {

    /**
     * Gemini-671 : @File - @Folders Add a reference to a folder
     * Users can search and mark folders when asking chat questions.
     */
    @Test
    void testFolderSelect() throws IOException {
        RobotUtil.clickFullScreenKey();
        UiUtil.openResourceProjectForTest(driver, wait);
        LeftManuBar leftManuBar = new LeftManuBar(driver, wait);
        ChatWindow chatWindow = leftManuBar.openGeminiChat();
        chatWindow.stepInto();
        WebElement chatInput = chatWindow.getChatInputField();
        chatInput.sendKeys("@");
        TimeUtil.doDelay(1000);
        List<WebElement> containerList = chatWindow.getContainerList(true);
        assertNotNull(containerList, "Container list should not be null");
        assertFalse(containerList.isEmpty(), "Container list should not be empty");

        TimeUtil.doDelay(1000);
        WebElement downIcon = chatWindow.getFolderDownIcon();
        downIcon.click();
        TimeUtil.doDelay(1000);
        WebElement folderItem = chatWindow.getFolderItem("test-plan-resource");
        assertTrue(folderItem.isDisplayed(), "Failed to get test-plan-resource folder item.");
        TimeUtil.doDelay(1000);
        WebElement upIcon = chatWindow.getFolderUpIcon();
        upIcon.click();
        TimeUtil.doDelay(1000);
        containerList = chatWindow.getContainerList(true);
        assertNotNull(containerList, "Container list should not be null");
        assertFalse(containerList.isEmpty(), "Container list should not be empty");
        TimeUtil.doDelay(1000);
        chatInput.sendKeys("Code Gen");
        TimeUtil.doDelay(1000);
        folderItem = chatWindow.getFolderItem("Code Generation");
        folderItem.click();
        TimeUtil.doDelay(1000);
        String prompt = chatInput.getText();
        log.debug("prompt: {}", prompt);
        assertTrue(prompt.contains("@Code Generation"));
        chatWindow.getSubmitButton().click();
        TimeUtil.doDelay(1000);
        containerList = chatWindow.getContainerList(false);
        assertTrue(containerList.isEmpty(), "Container list should be empty");
    }
}