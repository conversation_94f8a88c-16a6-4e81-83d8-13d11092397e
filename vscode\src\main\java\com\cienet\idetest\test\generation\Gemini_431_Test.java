package com.cienet.idetest.test.generation;

import com.cienet.idetest.util.TestExceptionHandler;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.IOException;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_431_Test extends BaseGenerationTest {

    @Test
    void testCodeGen() throws IOException {
        String filePath = "Code Generation/C++/quickSort.cpp";
        String text = "// Given an array of integers nums, sort the array in ascending order using quick sort and return it";
        testCodeGenByText(filePath, text);
    }
}
