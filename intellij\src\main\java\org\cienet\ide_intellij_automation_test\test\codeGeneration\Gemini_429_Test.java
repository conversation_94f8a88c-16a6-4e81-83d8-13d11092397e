package org.cienet.ide_intellij_automation_test.test.codeGeneration;

import com.cienet.ide.test.common.vo.LanguageTypeEnum;
import org.cienet.ide_intellij_automation_test.util.TestExceptionHandler;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.IOException;


@ExtendWith(TestExceptionHandler.class)
public class Gemini_429_Test extends BaseGenerationTest {
    
    @Test
    void Gemini_429() throws IOException {
        doSimpleGenerationTest(LanguageTypeEnum.C);
    }
}
