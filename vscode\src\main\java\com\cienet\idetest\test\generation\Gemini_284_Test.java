package com.cienet.idetest.test.generation;

import com.cienet.idetest.test.ui.SuggestPreview;
import com.cienet.idetest.util.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;

import java.io.IOException;

import static com.cienet.idetest.util.UiUtil.findElement;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
@ExtendWith(TestExceptionHandler.class)
public class Gemini_284_Test extends BaseGenerationTest {

    /**
     * Gemini-284 : Accept line by keybind. - Version4
     * To validate "accept line" function work well.
     *
     * @throws IOException
     */
    @Test
    void testAcceptLine() throws IOException {
        setAcceptLineHotKey();

        // Given
        UiUtil.openResourceProjectForTest(driver, wait, "Generation/Python/empty.py");
        clearEditorText();

        // When
        WebElement editArea = findElement(wait, "//*[contains(@class,'editor-scrollable')]");
        actions.sendKeys("// Write a function to check if a number is prime or not.").perform();
        TimeUtil.doDelay(500);
        actions.sendKeys(Keys.ENTER).perform();
        String originalCode = editArea.getText();
        TimeUtil.doDelay(3000);

        // Trigger Code Generation by clicking Ctrl+Enter
        actions.keyDown(Keys.CONTROL).keyDown(Keys.ENTER).perform();
        TimeUtil.doDelay(3000);

        // Check suggested code
        SuggestPreview suggestPreview = new SuggestPreview(driver, wait);
        String origCurrentLine = suggestPreview.getCurrentLine();
        String origPreviewCode = suggestPreview.getPreviewCode();

        // Accept a line in suggested code.
        Keys altKey = CommonUtil.isMacOs() ? Keys.COMMAND : Keys.ALT;
        actions.keyDown(Keys.CONTROL).keyDown(altKey).sendKeys("l").keyUp(altKey).keyUp(Keys.CONTROL).perform();
        TimeUtil.doDelay(2000);

        String newCurrentLine = suggestPreview.getCurrentLine();
        String newPreviewCode = suggestPreview.getPreviewCode();

        assertNotEquals(newCurrentLine, origCurrentLine, "Accept Line function seems not working.");
        assertTrue(newPreviewCode.length() < origPreviewCode.length(), "The length of preview code is not reduced.");

        String finalCode = editArea.getText();

        // Then
        assertTrue(finalCode.length() - originalCode.length() > 5,
                "Accept Line function seems not working. Final code=" + finalCode);
        TimeUtil.doDelay(3000);
    }

    private void setAcceptLineHotKey() {
        String altKeyVal = CommonUtil.isMacOs() ? "cmd" : "alt";
        String fileConent = "[{\n\"key\": \"ctrl+" + altKeyVal + "+l\",\n"
                + "\"command\": \"editor.action.inlineSuggest.acceptNextLine\"\n}]\n";
        VSCodeUtil.createConfigJson("keybindings.json", fileConent);
    }

    /*
    private void setAcceptLineHotKey(Keys ctrlKey) {
        // Set Accept Line hotkey
        actions.keyDown(ctrlKey).keyDown(Keys.SHIFT).sendKeys("p").keyUp(Keys.SHIFT).keyUp(ctrlKey).perform();
        actions.sendKeys("Preferences: Open Keyboard Shortcuts (JSON)").perform();
        TimeUtil.doDelay(3000);
        WebElement element = findElement(wait, "//div[contains(@class, 'quick-input-list')]"
                + "//div[contains(@class, 'monaco-list-row') and contains(@aria-label,'Preferences: Open Keyboard Shortcuts (JSON)')]");
        element.click();
        TimeUtil.doDelay(2000);
        UiUtil.clearNotificatons(driver);
        TimeUtil.doDelay(2000);
        WebElement editorPane = findElement(wait, "//div[contains(@class, 'view-lines')]");
        editorPane.click();
        actions.moveToElement(editorPane);
        TimeUtil.doDelay(500);
        clearEditorText();
        TimeUtil.doDelay(500);
        actions.sendKeys("// Place your key bindings in this file to override the defaults").sendKeys(Keys.ENTER).perform();
        TimeUtil.doDelay(500);
        actions.sendKeys("[{  ").sendKeys(Keys.ESCAPE).perform();

        String altKeyVal = CommonUtil.isMacOs() ? "cmd" : "alt";
        actions.sendKeys("\"key\": \"ctrl+" + altKeyVal + "+l\",\n"
                       + "\"command\": \"editor.action.inlineSuggest.acceptNextLine\"\n\n").perform();
        TimeUtil.doDelay(500);
        actions.sendKeys(Keys.ESCAPE);
        actions.sendKeys("}]");
        TimeUtil.doDelay(500);
        actions.sendKeys(Keys.ESCAPE);
        TimeUtil.doDelay(500);

        // Save hot key settings
        actions.keyDown(ctrlKey).sendKeys("s").keyUp(ctrlKey).perform();
        TimeUtil.doDelay(1000);
    }
   */

}
