package com.cienet.idetest.test.completion;

import com.cienet.idetest.test.BaseTest;
import com.cienet.idetest.test.ui.EditorArea;
import com.cienet.idetest.test.ui.SuggestPreview;
import com.cienet.idetest.util.TimeUtil;
import com.cienet.ide.test.common.vo.LanguageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static com.cienet.idetest.util.UiUtil.findElement;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
public class BaseCompletionTest extends BaseTest {

    protected void doCompletionTest(String filePath, String comment, String funcDeclare) throws IOException {
        SuggestPreview suggestPreview = new SuggestPreview(driver, wait, actions);
        String promptText = suggestPreview.triggerSuggest(filePath, comment, funcDeclare);
        int promptLength = promptText.length();
        // WebElement acceptLabel = suggestPreview.getAcceptLabel();
        // It causes error in Gemini_428.
        String suggestCode = suggestPreview.getSuggestCode();
        log.debug("suggestCode: {}", suggestCode);
        assertTrue(StringUtils.isNoneEmpty(suggestCode), "No code is suggested!");
        actions.sendKeys(Keys.TAB).perform();
        TimeUtil.doDelayInSeconds(1);
        EditorArea editorArea = new EditorArea(driver, wait, actions);
        WebElement editAreaElem = editorArea.getEditorArea();
        String finalText = editAreaElem.getText();
        int finalLength = finalText.length();
        log.debug("finalLength: {}, finalText: {}", finalLength, finalText);

        // Then
        log.debug("original promptLength={}, finalLength={}", promptLength, finalLength);
        assertTrue(finalLength > promptLength, String.format("Code completion failed. Prompt:\n%s\n", finalText));
        TimeUtil.doDelayInSeconds(3);
    }

}
