package com.cienet.idetest.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.junit.jupiter.api.extension.LifecycleMethodExecutionExceptionHandler;
import org.junit.jupiter.api.extension.TestExecutionExceptionHandler;
import org.openqa.selenium.WebDriver;

@Slf4j
public class TestExceptionHandler implements LifecycleMethodExecutionExceptionHandler, TestExecutionExceptionHandler {

    @Override
    public void handleAfterAllMethodExecutionException(ExtensionContext context, Throwable throwable) throws Throwable {
        log.error("enter handleAfterAllMethodExecutionException()...", throwable);
        handleException(context);
        LifecycleMethodExecutionExceptionHandler.super.handleAfterAllMethodExecutionException(context, throwable);
    }

    @Override
    public void handleAfterEachMethodExecutionException(ExtensionContext context, Throwable throwable)
            throws Throwable {
        log.error("enter handleAfterEachMethodExecutionException()...", throwable);
        handleException(context);
        LifecycleMethodExecutionExceptionHandler.super.handleAfterEachMethodExecutionException(context, throwable);
    }

    @Override
    public void handleBeforeAllMethodExecutionException(ExtensionContext context, Throwable throwable)
            throws Throwable {
        log.error("enter handleBeforeAllMethodExecutionException()...", throwable);
        handleException(context);
        LifecycleMethodExecutionExceptionHandler.super.handleBeforeAllMethodExecutionException(context, throwable);
    }

    @Override
    public void handleBeforeEachMethodExecutionException(ExtensionContext context, Throwable throwable)
            throws Throwable {
        log.error("enter handleBeforeEachMethodExecutionException()...", throwable);
        handleException(context);
        LifecycleMethodExecutionExceptionHandler.super.handleBeforeEachMethodExecutionException(context, throwable);
    }

    @Override
    public void handleTestExecutionException(ExtensionContext context, Throwable throwable) throws Throwable {
        log.error("enter handleTestExecutionException()...", throwable);
        handleException(context);

        throw throwable;
    }

//    private WebDriver getDriverFromContext(ExtensionContext context) throws Throwable {
//        Object testInstance = context.getRequiredTestInstance();
//        Field field = testInstance.getClass().getDeclaredField("driver");
//        field.setAccessible(true);
//        WebDriver driver = (WebDriver) field.get(testInstance);
//        return driver; // always get null. Why???
//    }

    private void handleException(ExtensionContext context) throws Throwable {
        String testClassName = context.getTestClass().map(Class::getSimpleName)
                .orElse("UnknownClass"); // ex: Gemini_428_Test
        log.debug("enter handleException() for {}, resultOid={}", testClassName, SessionUtil.getResultOid());
        log.error("Execution exception:", context.getExecutionException());

        // WebDriver driver = getDriverFromContext(context);
        WebDriver driver = SessionUtil.getDriver();
        if (driver == null) {
            return;
        }

        VSCodeUtil.doScreenShot(testClassName);

        // close the VSC app since tearDown() is not called when exception is thrown
        VSCodeUtil.closeDriver(driver);
    }
}
