package com.cienet.idetest.test.inline;

import com.cienet.idetest.test.ui.DiffView;
import com.cienet.idetest.util.RobotUtil;
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
import com.cienet.idetest.util.UiUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;

import java.io.IOException;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_663_Test extends BaseInlineTest {

    private final By inlineChatFix = By.xpath("//*[contains(@class,'quick-input-widget show-file-icons')]//*[contains(text(),'/fix')]");

    @Test
    public void testInlineChat() throws IOException {
        // Given
        RobotUtil.clickFullScreenKey();
        UiUtil.openResourceProjectForTest(driver, wait, "Chat/empty.py");

        // When & Then
        clearEditorText();
        String prompt =
                  "def find_odd_numbers(arr, start, end):\n"
                + "    odd_numbers =  ]\n"  // missing right bracket
                + "    for i in range(start, end):\n"
                + "        if arr[i] % 2 != 0:\n"
                + "            odd_numbers.append(arr[i])\n"
                + "    return odd_numbers\n";
        actions.sendKeys(prompt).perform();
        TimeUtil.doDelay(1000);

        assertFalse(inlineChatFixOptionExist());
        // No need to wait for GCA inline chat prompt when there is already text typed into the editor.
        WebElement inlineEle = triggerInlineChat(false); // trigger in-line chat
        assertTrue(inlineChatFixOptionExist());

        // click /fix option
        WebElement fixOptionEle = driver.findElement(inlineChatFix);
        fixOptionEle.click();
        TimeUtil.doDelay(500);
        // after click, the "/fix" should be inserted to the textarea automatically
        assertTrue(inlineEle.getAttribute("value").contains("/fix"));

        actions.sendKeys(inlineEle, "missing brackets in my code").perform();
        TimeUtil.doDelay(500);
        RobotUtil.clickEnterKey();
        TimeUtil.doDelay(5000);
        DiffView diffView = new DiffView(driver, wait, "empty.py");

        // validate source & diff-view both exist
        // Find the source code in the left
        WebElement sourcePane = diffView.getSourcePane();
        assertTrue(sourcePane.isDisplayed());

        // Find the suggested source code in the right
        WebElement suggestedPane = diffView.getSuggestedPane();
        assertTrue(suggestedPane.isDisplayed());

        // unexpected-closing-bracket should be catached in diff-view
        UiUtil.findElement(wait, "//*[contains(@class,'unexpected-closing-bracket')]");
    }

    private boolean inlineChatFixOptionExist() {
        List<WebElement> elements = driver.findElements(inlineChatFix);
        return !elements.isEmpty();
    }

}
