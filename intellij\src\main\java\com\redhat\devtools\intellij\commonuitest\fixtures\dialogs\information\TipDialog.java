/*******************************************************************************
 * Copyright (c) 2020 Red Hat, Inc.
 * Distributed under license by Red Hat, Inc. All rights reserved.
 * This program is made available under the terms of the
 * Eclipse Public License v2.0 which accompanies this distribution,
 * and is available at https://www.eclipse.org/legal/epl-v20.html
 *
 * Contributors:
 * Red Hat, Inc. - initial API and implementation
 ******************************************************************************/
package com.redhat.devtools.intellij.commonuitest.fixtures.dialogs.information;

import com.intellij.remoterobot.RemoteRobot;
import com.intellij.remoterobot.data.RemoteComponent;
import com.intellij.remoterobot.fixtures.CommonContainerFixture;
import com.intellij.remoterobot.fixtures.DefaultXpath;
import com.intellij.remoterobot.fixtures.FixtureName;
import com.intellij.remoterobot.fixtures.JCheckboxFixture;
import com.redhat.devtools.intellij.commonuitest.utils.constants.ButtonLabels;
import com.redhat.devtools.intellij.commonuitest.utils.constants.XPathDefinitions;
import org.jetbrains.annotations.NotNull;

/**
 * Tip of the Day dialog fixture
 *
 * <AUTHOR>
 */
@DefaultXpath(by = "MyDialog type", xpath = XPathDefinitions.TIP_DIALOG)
@FixtureName(name = "Tip Of The Day Dialog")
public class TipDialog extends CommonContainerFixture {
    public TipDialog(@NotNull RemoteRobot remoteRobot, @NotNull RemoteComponent remoteComponent) {
        super(remoteRobot, remoteComponent);
    }

    /**
     * Get the 'Don't show tips' checkbox fixture
     *
     * @return checkbox fixture
     */
    public JCheckboxFixture dontShowTipsCheckBox() {
        return checkBox("Don't show tips", true);
    }

    /**
     * Close the 'Tip of the Day' dialog by clicking on the 'Close' button
     */
    public void close() {
        button(ButtonLabels.CLOSE_LABEL).click();
    }
}