package com.cienet.idetest.test.completion;

import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;

import java.io.IOException;
import java.util.List;

import static com.cienet.idetest.util.UiUtil.findElement;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_60_Test extends BaseCompletionTest {

    @Test
    public void test() throws IOException {
        // given
        UiUtil.openResourceProjectForTest(driver, wait, "Chat/empty.py");
        actions.keyDown(Keys.COMMAND).keyDown(Keys.SHIFT).sendKeys("p").keyUp(Keys.COMMAND).keyUp(Keys.SHIFT).perform();
        WebElement quickMenu = findElement(wait, By.cssSelector("[aria-describedby='quickInput_message']"));
        quickMenu.sendKeys("new file");
        actions.sendKeys(Keys.ENTER).sendKeys(Keys.ENTER).perform();

        // when
        actions.sendKeys("for file in os.listdir(src_folder):").perform();
        TimeUtil.doDelay(3000);
        actions.sendKeys(Keys.ENTER).perform();
        actions.keyDown(Keys.CONTROL).keyDown(Keys.ENTER).keyUp(Keys.CONTROL).keyUp(Keys.ENTER).perform();
        TimeUtil.doDelay(3000);
        actions.sendKeys(Keys.TAB).perform();
        TimeUtil.doDelay(1000);
        List<WebElement> codeLines = driver.findElements(By.className("view-line"));

        // Then
        assertTrue(codeLines.size() >= 2);
        assertTrue(codeLines.stream().anyMatch(line -> line.getText().contains("print")));
    }

}
