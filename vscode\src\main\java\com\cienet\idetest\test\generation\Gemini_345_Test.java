package com.cienet.idetest.test.generation;

import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.test.ui.RightClickContextMenu;
import com.cienet.idetest.util.TestExceptionHandler;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;

import java.io.IOException;
import java.util.List;

import static com.cienet.idetest.util.UiUtil.findElement;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_345_Test extends BaseGenerationTest {

    @Test
    void test() throws IOException {
        // Given
        UiUtil.openResourceProjectForTest(driver, wait, "Chat/empty.py");
        clearEditorText();

        // When
        actions.sendKeys("// write a function that checks if input is an even number").perform();
        actions.sendKeys(Keys.ENTER).perform();
        RightClickContextMenu rightClickContextMenu = new RightClickContextMenu(driver, wait);
        WebElement presentation = findElement(wait, By.cssSelector("div[role='presentation']"));
        WebElement acceptSuggestionButton = rightClickContextMenu.triggerCodeGenFromContextMenu(wait, presentation, true);
        acceptSuggestionButton.click();
        List<WebElement> codeLines = driver.findElements(By.className("view-line"));

        // Then
        assertTrue(codeLines.size() >= 4);
    }

}
