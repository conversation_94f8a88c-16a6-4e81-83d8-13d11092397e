package com.cienet.idetest.test.ui;

import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.support.ui.WebDriverWait;

import static com.cienet.idetest.util.UiUtil.findElement;

public class LeftManuBar {

    private WebDriver driver;

    private WebDriverWait wait;

    public LeftManuBar(WebDriver driver, WebDriverWait wait) {
        this.driver = driver;
        this.wait = wait;
    }

    public OpenProjectMenu clickOpenProjectButton() {
        findElement(wait, By.xpath("//a[@class='action-label codicon codicon-explorer-view-icon']")).click();
        return new OpenProjectMenu(driver, wait);
    }

    public ChatWindow openGeminiChat() {
        findElement(wait, "//*[contains(@aria-label, 'Gemini Code Assist')]").click();
        return new ChatWindow(driver, wait);
    }
}
