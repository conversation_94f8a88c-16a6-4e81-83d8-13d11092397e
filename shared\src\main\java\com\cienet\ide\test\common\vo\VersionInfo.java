package com.cienet.ide.test.common.vo;

public record VersionInfo(String ideVersion, String pluginVersion) {

    public boolean bothValuePresented() {
        return ideVersion != null && !ideVersion.isEmpty() &&
                pluginVersion != null && !pluginVersion.isEmpty() &&
                !"unknown".equals(ideVersion) && !"unknown".equals(pluginVersion);
    }

    @Override
    public String toString() {
        return String.format("VersionInfo{ideVersion='%s', pluginVersion='%s'}", ideVersion, pluginVersion);
    }
}