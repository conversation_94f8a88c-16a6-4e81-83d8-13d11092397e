# IntelliJ IDE Test Automation

This project demonstrates automation testing for IntelliJ IDE using Remote Robot framework. It focuses on testing various IDE behaviors including code completion, code generation, and other AI-assisted development features.

## Table of Contents

- [Quick Overview](#quick-overview)
- [Technical Stack](#technical-stack)
- [Setup Guide](#setup-guide)
- [Manual Plugin Installation Flow](#manual-plugin-installation-flow-for-release-candidate-builds)
- [Key Components and Interactions](#key-components-and-interactions)
- [Test Case Implementation](#test-case-implementation)
- [Test Case Execution](#test-case-execution)
- [Automation Capabilities](#automation-capabilities)
- [Best Practices](#best-practices)
- [Limitations](#limitations)

## Quick Overview

```mermaid
flowchart TB
    %% USER INTERFACE LAYER
    subgraph UI["User Interface"]
        UI_1("Swagger UI<br/>CI/CD System<br/>CLI Tools")
    end

    %% APPLICATION LAYER
    subgraph APP["Test Automation Agent"]
        direction TB

        subgraph API["API Layer"]
            direction TB
            API_1("Spring Boot API")
            API_2("Test Run Controller")
        end

        subgraph SVC["Service Layer"]
            direction TB
            SVC_1("IntelliJ Manager")
            SVC_2("Test Runner")
            SVC_3("Error Handler")
            SVC_4("Debug Log Collector")
        end

        subgraph EXE["Execution Layer"]
            direction TB
            subgraph TST_CASE["Test Cases"]
               TST_CASE_1@{ shape: procs, label: "Test Case Implementation, e.g., Gemini_15_Test.java, Gemini_47_Test.java, Gemini_105_Test.java ..."}
            end
            subgraph TST_FRM["Test Frameworks"]
               direction TB
               TST_FRM_1("JUnit 5 Engine")
               TST_FRM_2("IntelliJ Remote Robot Core")
            end
        end
    end

    %% IDE LAYER
    subgraph IDE["IDE Environment"]
        IDE_1("IntelliJ IDEA<br/>Gemini Code Assist")
    end

    %% FLOW CONNECTIONS
    UI e1@<==>|HTTP Requests / Responses| APP
    API e2@-->|Delegate Operations| SVC
    SVC e3@-->|Execute Tests| EXE
    EXE e4@-->|Results & Logs| SVC
    SVC e5@-->|Test Summary| API
    APP e6@<==>|UI Commands & Results| IDE

    %% STYLING DEFINITIONS
    classDef uiStyle fill:#e5e7eb,stroke:#94a3b8,stroke-width:2px,color:#1f2937,font-weight:bold
    classDef appStyle fill:#f3f4f6,stroke:#cbd5e1,stroke-width:2px,color:#1e293b,font-weight:bold
    classDef apiStyle fill:#f9fafb,stroke:#60a5fa,stroke-width:2px,color:#1e3a8a,font-weight:bold
    classDef svcStyle fill:#f1f5f9,stroke:#38bdf8,stroke-width:2px,color:#0c4a6e,font-weight:bold
    classDef exeStyle fill:#fef3c7,stroke:#facc15,stroke-width:2px,color:#78350f,font-weight:bold
    classDef ideStyle fill:#ecfdf5,stroke:#34d399,stroke-width:2px,color:#064e3b,font-weight:bold

    classDef nodeStyle fill:#ffffff,stroke:#d1d5db,stroke-width:1.5px,color:#1f2937,font-weight:500

    class UI uiStyle
    class APP appStyle
    class API apiStyle
    class SVC apiStyle
    class EXE apiStyle
    class TST_CASE svcStyle
    class TST_FRM svcStyle
    class IDE uiStyle

    class UI_1,API_1,API_2,SVC_1,SVC_2,SVC_3,SVC_4,TST_FRM_1,TST_FRM_2,TST_CASE_1,IDE_1 nodeStyle

    e1@{ animate: true }
    e2@{ animate: true }
    e3@{ animate: true }
    e4@{ animate: true }
    e5@{ animate: true }
    e6@{ animate: true }
```

### For More System Design Diagrams

- [Sequence Diagram](./docs/sequence-diagram.md)
- [Flowchart Diagram](./docs/flowchart-diagram-2.md)

## Technical Stack

### Core Framework

- **Spring Boot**: 3.2.0
- **Java**: JDK 21
- **Build Tool**: Gradle 8.5

### Testing Framework

- **IntelliJ Remote Robot**: 0.11.22
  - `com.intellij.remoterobot:remote-robot`
  - `com.intellij.remoterobot:remote-fixtures`
- **JUnit**: 5.9.2
- **Swagger**: 3.0.0 (for API documentation)

### Development Environment

- **IDE**: IntelliJ IDEA 2023.3+
- **OS**:
  - Windows 11 (Tested)
  - Linux (Tested)
  - macOS (Not tested)
- **Dependencies**:
  ```gradle
  implementation 'com.intellij.remoterobot:remote-robot:0.11.22'
  implementation 'com.intellij.remoterobot:remote-fixtures:0.11.22'
  implementation 'org.junit.jupiter:junit-jupiter:5.9.2'
  implementation 'io.swagger.core.v3:swagger-annotations:2.2.15'
  ```

## How to Build The Agent

Follow these steps to build IntelliJ Test Automation Agent:

1. **Clone Repositories**:

The `ide-test-automation/intellij` repository contains the main Java source code, while `test-plan-resource` stores sample code used in automated tests.

```bash
    git clone test-plan-resource
    git clone https://github.com/CIeNET-International/ide-test-automation
```
1. **Build The Agent**:

   The following command builds the agent product image `intellij.agent.tar.gz`, which includes the binary and sample code for testing.

   ```bash
       cd ide-test-automation/intellij
       ./gradlew clean prodTar
   ```

The file `intellij.agent.tar.gz` will be in `build/tar` directory.

## Agent Setup Guide

The following steps describe how to set up the automated agent on the devices you want to test.

1. **Extract intellij.agent.tar.gz**

   Create a testhome directory under your home directory, and extract the contents of `intellij.agent.tar.gz` into it. The archive includes several files and directories, including the startup scripts:

    - `startagent.sh` for macOS and Linux
    - `startagent.ps1` for Windows 11

   ```bash
    mkdir ~/testhome
    cd ~/testhome
    tar zxvf intellij.agent.tar.gz
   ```

1. **Install Java 21**

   Download OpenJDK 21 and install it in your testing device.

1. **Run the IntelliJ Automated Test Agent**

    - **_Start the Rest API Server_**

      In macOS and Linux:

      ```bash
      ./startagent.sh
      ```

      In Windows 11:

      ```bash
      .\startagent.ps1
      ```

      A Spring Boot based Rest API server will be started and listening to the request.

1. **Swagger UI**:

   Once the application is up and running, detailed API documentation is available through Swagger. Visit [http://localhost:8080/swagger-ui/index.html](http://localhost:8080/swagger-ui/index.html) to explore the available endpoints and their usage.

1. **IntelliJ Version**
 
   The agent will use IntelliJ 2025.1.3 by default. You can switch to another version of IntelliJ by setting the environment IDEA_VERSION.
   For example: 
   ```bash
   export IDEA_VERSION="2025.1.1.1"
   ```

### Possible additional step for Mac
**Additional permissions for Intellij** - Depending on your system configuration and user permissions, Intellij may require you to manually add additional permissions to control the mouse, the tests control the cursor on the screen and may not work without these permissions.
> **To grant additional permissions**
> go to: **System Settings > Privacy & Security > Accessibility**, then make sure the toggle for the application is enabled.
If the app isn’t listed, you can click the "+" button to manually add it from the Applications folder.

## Manual Plugin Installation Flow (for Release Candidate Builds)

> **Note**: Use this flow when testing RC builds that require manual plugin installation from disk.

1.  Call `/tests/start` to launch the virtual IDE.
2.  Manually install the plugin from the local disk (**without clicking "Restart IDE"**).
3.  Close the IDE manually (e.g., Alt + F4 or by clicking the close icon).
4.  Call `/tests/start` once again to relaunch the virtual IDE
5.  Open the 'test-plan-resource' project
6.  Call `/tests/run/{testName}` to execute the test cases.

## Key Components and Interactions

### Dataflow Quick Summary

1. A user request hits the `TestRunController`.
2. The controller, after managing concurrency, delegates to `IntellijManagerService` (for IDE setup if needed) and `TestRunnerService` (for test execution).
3. `IntellijManagerService` uses Remote Robot to prepare the `IntelliJ IDEA` instance.
4. `TestRunnerService` uses JUnit to run `Actual Test Classes`.
5. These test classes use `IntelliJ Remote Robot` (often via Red Hat Common UI Test Utils) to drive the `IntelliJ IDEA` UI and interact with the `Gemini Code Assist Plugin`.
6. Results are collected by `TestRunnerService` and returned via the `TestRunController` to the user.

### Explaination

1.  **User Interaction**

    - Represents the external entity (a human user via Swagger UI, or an automated system like a CI/CD pipeline) that initiates test operations.
    - Interacts with the system by sending HTTP requests to the API Layer.

2.  **API Layer**

    - **[TestRunController.java](https://github.com/CIeNET-International/ide-test-automation/blob/main/intellij/src/main/java/org/cienet/ide_intellij_automation_test/controller/TestRunController.java)**
      - Built with Spring Boot.
      - Exposes REST endpoints (e.g., `/tests/run/{testName}`, `/tests/setup`).
      - Receives HTTP requests and is the primary entry point into the system.
      - Manages concurrency using a lock to ensure only one test operation (like setup or a test run) happens at a time.
      - Delegates the actual work to the Service Layer.

3.  **Service Layer**

    - **[IntellijManagerService.java](https://github.com/CIeNET-International/ide-test-automation/blob/main/intellij/src/main/java/org/cienet/ide_intellij_automation_test/service/IntellijManagerService.java)**
      - Responsible for the lifecycle of the IntelliJ IDEA instance.
      - Handles starting, stopping, and setting up the IDE (e.g., installing the Gemini Code Assist plugin, configuring settings).
      - Uses IntelliJ Remote Robot (RRC) for UI automation to perform these setup tasks within the IDE.
    - **[TestRunnerService.java](https://github.com/CIeNET-International/ide-test-automation/blob/main/intellij/src/main/java/org/cienet/ide_intellij_automation_test/service/TestRunnerService.java)**
      - Manages the test execution process.
      - Discovers available test classes (Tests) using reflection.
      - Uses the JUnit 5 Engine programmatically to run specific tests.
      - Collects results, logs (via [DebugLogFilter.java](https://github.com/CIeNET-International/ide-test-automation/blob/main/intellij/src/main/java/org/cienet/ide_intellij_automation_test/logging/DebugLogFilter.java)), and screenshots for failed tests, then packages this information into `TestSummary` objects (from the shared library) to be returned by the API.

4.  **Test Execution & IDE Interaction**

    - **JUnit 5 Engine**
      - The core JUnit framework that executes the test methods.
    - **Actual Test Classes**
      - These are the individual test scripts (e.g., [Gemini_15_Test.java](https://github.com/CIeNET-International/ide-test-automation/blob/main/intellij/src/main/java/org/cienet/ide_intellij_automation_test/test/codeGeneration/Gemini_15_Test.java)). They contain the logic for specific test scenarios.
      - They often extend a base class ([BaseTest.java](https://github.com/CIeNET-International/ide-test-automation/blob/main/intellij/src/main/java/org/cienet/ide_intellij_automation_test/test/BaseTest.java) or specific base tests like [BaseChatTest.java](https://github.com/CIeNET-International/ide-test-automation/blob/main/intellij/src/main/java/org/cienet/ide_intellij_automation_test/test/chat/BaseChatTest.java)) which likely includes parts of the Red Hat Common UI Test Utils for common setup.
      - They use IntelliJ Remote Robot Core and fixtures (some directly, some via Red Hat Common UI) to interact with the IntelliJ IDEA UI.
      - They specifically interact with and verify the functionality of the Gemini Code Assist Plugin (GCA) running inside the IDE.
    - **IntelliJ Remote Robot Core**
      - The fundamental library ([com.intellij.remoterobot](https://github.com/JetBrains/intellij-ui-test-robot)) that enables UI automation of IntelliJ IDEA. It sends commands to the Robot Server Plugin running in the IDE.
    - **Red Hat Common UI Test Utils**
      - A utility library (located under `intellij/src/main/java/com/redhat/devtools/intellij/commonuitest/`) built on top of the IntelliJ Remote Robot Core, providing higher-level abstractions, custom fixtures for IntelliJ UI elements (e.g., [FlatWelcomeFrame.java](https://github.com/CIeNET-International/ide-test-automation/blob/main/intellij/src/main/java/com/redhat/devtools/intellij/commonuitest/fixtures/dialogs/FlatWelcomeFrame.java)), and potentially base test classes (e.g., [AbstractLibraryBaseTest.java](https://github.com/CIeNET-International/ide-test-automation/blob/main/intellij/src/main/java/com/redhat/devtools/intellij/commonuitest/AbstractLibraryBaseTest.java)).
    - **Gemini Code Assist Plugin (GCA)**
      - The IntelliJ plugin whose features are being tested.

5.  **IntelliJ IDEA Instance**
    - The actual IntelliJ IDEA application that is launched and controlled by the system.
    - It runs a Robot Server Plugin (configured via [build.gradle.kts](https://github.com/CIeNET-International/ide-test-automation/blob/main/intellij/build.gradle.kts)'s `runIdeForUiTests` task), which listens for commands from the IntelliJ Remote Robot Core.
    - The Gemini Code Assist Plugin (GCA) is installed and runs within this IDE instance.

## Test Case Implementation

1. **Code Generation**

   ```java
   // Objective: Test AI-assisted code generation
   // Example: Generate sorting algorithm
   keyboard.hotKey(KeyEvent.VK_CONTROL, KeyEvent.VK_SHIFT, KeyEvent.VK_N);
   editor.rightClick();
   menu.findText("Gemini").click();
   ```

2. **Code Completion**

   ```java
   // Objective: Test AI-powered code suggestions
   // Example: Trigger code completion
   keyboard.key(KeyEvent.VK_TAB);
   ```

3. **Unit Test Generation**

   ```java
   // Objective: Test automated test generation
   // Example: Generate unit tests
   editor.rightClick();
   menu.findText("Generate Test").click();
   ```

## Test Case Execution

Follow these steps to set up and run the tests:

1. **Start IntelliJ IDE**:

   - Run API endpoint: `/tests/start`
   - This will launch the IntelliJ IDE instance

2. **Setup Google Cloud AI (GCA)**:

   - Run API endpoint: `/tests/setup`
   - This will install GCA and restart the IDE

3. **Manual Setup Steps**:

   - Open the 'test-plan-resource-main' project
   - Log in to GCA using your Google account
   - Select your cloud project

4. **Get Available Test Cases**:

   - Run API endpoint: `/tests/getAvailableTestCases`
   - This will return a list of all available test cases

5. **Run Specific Test Case**:
   - Run API endpoint: `/tests/run/{testName}`
   - Replace `{testName}` with the name of the test case you want to execute

> **Note**: Ensure all prerequisites are met before starting the test execution process.

## Automation Capabilities

### Successfully Automated Features

| Feature              | Notes                                       |
| -------------------- | ------------------------------------------- |
| Code Completion      | Using keyboard shortcuts and text input     |
| Code Generation      | Via right-click menu and keyboard shortcuts |
| Output Log Gathering | Through IDE's logging system                |

### GCA Communication Capabilities

The following GCA features are fully functional and can be automated without issues:

- Code Generation
- Code Explanation
- Code Completion
- Code Transformation

## Best Practices

1. **Timing Management**:

   - Use appropriate wait times
   - Implement retry mechanisms
   - Handle timeout scenarios

2. **Error Handling**:

   - Catch and log exceptions
   - Implement fallback mechanisms
   - Provide detailed error messages

3. **Maintenance**:
   - Regular updates for IDE compatibility
   - Documentation updates
   - Test case reviews

## Limitations

1. **Project Access**:

   - Cannot automate opening existing projects
   - Path dependency issues due to different folder structures across machines
   - Manual project setup required

2. **Plugin Stability**:

   - GCA plugin may uninstall after system restart
   - Requires manual reinstallation in some cases
   - Plugin state not consistently maintained

3. **POC Timeline Constraints**:
   - Limited time for library research and implementation
   - Potential limitations may arise during future development
   - Some features may require additional research and testing
