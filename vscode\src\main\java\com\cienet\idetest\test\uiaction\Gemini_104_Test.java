package com.cienet.idetest.test.uiaction;

import com.cienet.idetest.test.ui.BottomBar;
import com.cienet.idetest.test.ui.PopUpWindow;
// import org.junit.jupiter.api.AfterEach; // Removed
// import org.junit.jupiter.api.BeforeEach; // Removed
import org.junit.jupiter.api.Test;
import org.openqa.selenium.Keys;
// import org.openqa.selenium.WebDriver; // Removed
// import org.openqa.selenium.interactions.Actions; // Removed
// import org.openqa.selenium.support.ui.WebDriverWait; // Removed

// import java.io.IOException; // Removed
// import java.time.Duration; // Removed

// import static com.cienet.idetest.util.UiUtil.prepareTestWithOpenProject; // Already removed
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.cienet.idetest.test.uiaction.BaseUiActionTest;

public class Gemini_104_Test extends BaseUiActionTest {

    // Fields driver, wait, actions are inherited from BaseUiActionTest
    // setup() method is inherited from BaseUiActionTest

    @Test
    public void test() {
        // Given
        BottomBar bottomBar = new BottomBar(driver, wait);

        // When
        PopUpWindow popUpWindow = bottomBar.openPopUpFromStarIcon();
        popUpWindow.openDocumentation();
        actions.sendKeys(Keys.ENTER).perform();

        // Then
        assertEquals(1, driver.getWindowHandles().size());
    }

    // tearDown() method is inherited from BaseTest (via BaseUiActionTest)
    // and handles driver.quit()

}
