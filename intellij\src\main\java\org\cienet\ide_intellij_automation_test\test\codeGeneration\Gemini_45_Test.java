package org.cienet.ide_intellij_automation_test.test.codeGeneration;

import com.intellij.remoterobot.fixtures.EditorFixture;
import com.intellij.remoterobot.fixtures.dataExtractor.RemoteText;
import com.intellij.remoterobot.utils.Keyboard;
import org.cienet.ide_intellij_automation_test.util.CommonUtil;
import org.cienet.ide_intellij_automation_test.util.TestExceptionHandler;
import org.cienet.ide_intellij_automation_test.util.TimeUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.event.KeyEvent;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertTrue;


@ExtendWith(TestExceptionHandler.class)
public class Gemini_45_Test extends BaseGenerationTest {

    private static final Logger log = LoggerFactory.getLogger(Gemini_45_Test.class);

    @Test
    void Gemini_45() throws IOException {
        openEmptyFileWithMultiplesFiles();
        String prompt = """
            # write a function to add two numbers and return the result
            """;

        EditorFixture editor = CommonUtil.prepareEditor(remoteRobot);
        Keyboard keyboard = new Keyboard(remoteRobot);

        CommonUtil.enterTextByLines(keyboard, prompt);

        RemoteText promptText = editor.getData().getAll().getLast();

        CommonUtil.triggerCodeGeneration(remoteRobot, keyboard, 30);

        promptText.moveMouse();
        TimeUtil.doDelayInSeconds(3);

        assertTrue(
            CommonUtil.hasCompletionDialog(remoteRobot), 
            String.format("Code generation failed after Alt+G trigger. " +
                "Expected completion dialog with 'to complete' text not found within 5 seconds. " +
                "Prompt: '%s'", prompt.trim())
            );
    }
}