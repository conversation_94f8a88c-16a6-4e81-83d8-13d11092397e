package com.cienet.idetest.test.chat;

import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.test.ui.ChatWindow;
import com.cienet.idetest.test.ui.RightClickContextMenu;
import com.cienet.idetest.util.TestExceptionHandler;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;

import java.io.IOException;

import static com.cienet.idetest.util.UiUtil.findElement;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_174_Test extends BaseChatTest {

    /**
     * Gemini-174 : Explain This without Selection - Version2
     * "Explain This" code without a highlighted selection
     *
     * Reviewed with test team on 17/10/2024
     * Test compared with TesLink on: 3/10/2024
     * @throws IOException
     */
    @Test
    void test() throws IOException {
        // Given
        UiUtil.openResourceProjectForTest(driver, wait, "Chat/two_sum.py");

        // When
        WebElement presentation = findElement(wait, By.cssSelector("div[role='presentation']"));
        RightClickContextMenu rightClickContextMenu = new RightClickContextMenu(driver, wait);
        ChatWindow chatWindow = rightClickContextMenu.triggerCodeExplain(presentation);
        WebElement chatResponse = chatWindow.waitUntilChatFinish();

        // Then
        String resp = chatResponse.getText();
        assertTrue(resp.contains("Explanation") || resp.contains("Summary") || resp.contains("breakdown"));
        assertFalse(resp.contains("can't"));
    }

}