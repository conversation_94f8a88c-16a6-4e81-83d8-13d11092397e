package org.cienet.ide_intellij_automation_test.test.codeGeneration;

import static org.junit.jupiter.api.Assertions.assertTrue;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

import org.cienet.ide_intellij_automation_test.test.BaseTest;
import org.cienet.ide_intellij_automation_test.util.TimeUtil;
import com.cienet.ide.test.common.vo.LanguageTypeEnum;
import org.cienet.ide_intellij_automation_test.util.CommonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.intellij.remoterobot.fixtures.EditorFixture;
import com.intellij.remoterobot.utils.Keyboard;
import static com.intellij.remoterobot.search.locators.Locators.byXpath;

import java.awt.event.KeyEvent;

public class BaseGenerationTest extends BaseTest {
  private static final Logger log = LoggerFactory.getLogger(BaseGenerationTest.class);
  private static Map<LanguageTypeEnum, String> _testFilenameMap = new HashMap<>();
  private static Map<LanguageTypeEnum, String> _testPromptMap = new HashMap<>();

  static {
    _testFilenameMap.put(LanguageTypeEnum.Typescript, "quickSort.ts");
    _testFilenameMap.put(LanguageTypeEnum.Go, "quick_sort.go");
    _testFilenameMap.put(LanguageTypeEnum.Javascript, "quickSort.js");
    _testFilenameMap.put(LanguageTypeEnum.Java, "QuickSort.java");
    _testFilenameMap.put(LanguageTypeEnum.Python, "quick_sort.py"); // Gemini_105
    _testFilenameMap.put(LanguageTypeEnum.C, "quickSort.c");

    _testPromptMap.put(LanguageTypeEnum.Python,
        "# Given an array of integers nums, sort the array in ascending order using quick sort and return it");
    _testPromptMap.put(LanguageTypeEnum.Java,
        "// Given an array of integers nums, sort the array in ascending order using quick sort and return it");
    _testPromptMap.put(LanguageTypeEnum.C,
        "// Given an array of integers nums, sort the array in ascending order using quick sort and return it");
  }

  protected void doSimpleGenerationTest(LanguageTypeEnum languageType) {

    log.info("Start excute generation test");
    //closeAllCurrentTabs();

    String currentFile = _testFilenameMap.get(languageType);
    openMultiplesFiles(currentFile);

    String prompt = _testPromptMap.get(languageType);

    EditorFixture editor = remoteRobot.find(
        EditorFixture.class,
        byXpath("//div[@class='EditorComponentImpl']"),
        Duration.ofSeconds(10));

    editor.setText("");

    editor.click();
    Keyboard keyboard = new Keyboard(remoteRobot);
    String[] promptArray = prompt.split("\n");
    int index = 0;
    for (String line : promptArray) {
      keyboard.enterText(line, 20);
      index++;
      if (index < promptArray.length) {
        keyboard.enter();
      }
    }
    keyboard.enter();
    int promptLength = editor.getText().length();
    CommonUtil.triggerCodeGeneration(remoteRobot, keyboard, 30);
    keyboard.key(KeyEvent.VK_TAB);
    String finalText = editor.getText();
    int finalLength = finalText.trim().length();

    assertTrue(finalLength > promptLength,
        String.format("Code generation failed. Prompt:\n%s\n", finalText));
  }

  protected void openMultiplesFiles(String currentFile) {
    // for (String file : _testFilenameMap.values()) {
    //   if (file != currentFile) {
    //     openResourceProjectForTest(file);
    //   }
    // }
    openResourceProjectForTest(currentFile);
  }

  protected void openEmptyFileWithMultiplesFiles() {
    openMultiplesFiles("Chat/empty.py");
  }
}
