package com.cienet.idetest.test.uiaction;

import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
// import org.junit.jupiter.api.AfterEach; // Removed
// import org.junit.jupiter.api.BeforeEach; // Removed
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
// import org.openqa.selenium.WebDriver; // Removed
import org.openqa.selenium.WebElement;
// import org.openqa.selenium.support.ui.WebDriverWait; // Removed

// import java.io.IOException; // Removed
// import java.time.Duration; // Removed

import static com.cienet.idetest.util.UiUtil.findElement;
// import static com.cienet.idetest.util.UiUtil.prepareTestWithOpenProject; // Removed
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.cienet.idetest.test.uiaction.BaseUiActionTest;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_37_Test extends BaseUiActionTest {
    // Fields driver, wait are inherited from BaseUiActionTest
    // setup() method is inherited from BaseUiActionTest

    @Test
    public void test() {
        // When
        WebElement extensionsButton = findElement(wait, By.className("codicon-extensions-view-icon"));
        extensionsButton.click();
        TimeUtil.doDelay(4000);
        WebElement cloudConsoleExtension = driver.findElement(By.xpath("//*[contains(@aria-label, 'Gemini Code Assist + Google Cloud Code')]"));
        cloudConsoleExtension.click();
        WebElement disableButton = driver.findElement(By.cssSelector("[aria-label='Disable this extension']"));
        disableButton.click();
        WebElement restartExtensionButton = driver.findElement(By.cssSelector("[aria-label='Please restart extensions to disable this extension.']"));
        restartExtensionButton.click();
        WebElement codeTab = findElement(wait, By.xpath("//a[@class='label-name' and text()='backup_script_c.py']"));
        codeTab.click();

        // Then
        Exception exception = assertThrows(Exception.class, () -> {
            findElement(wait, By.cssSelector("[aria-label='Gemini Code Assist: Smart Actions']"));
        });
        assertTrue(exception.getMessage().contains("Expected condition failed: waiting for visibility of element located by By.cssSelector"));
    }

    // tearDown() method is inherited from BaseTest (via BaseUiActionTest)
    // and handles driver.quit()

}