package com.cienet.idetest.test.chat;

import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.test.ui.ChatWindow;
import com.cienet.idetest.test.ui.RightClickContextMenu;
import com.cienet.idetest.util.TestExceptionHandler;
import com.cienet.idetest.util.TimeUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;

import java.io.IOException;

import static com.cienet.idetest.util.UiUtil.findElement;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_268_Test extends BaseChatTest {

    /**
     * Deactivate
     * Gemini-268 : Generate Unit Test in an empty file - Version1
     * To verify the generation of Unit Test in an empty file
     *
     * Test compared with TesLink on: 17/10/2024
     */
    @Test
    public void test() throws IOException {
        // Given
        String expectedMessagePart = "import unittest";
        UiUtil.openResourceProjectForTest(driver, wait, "Chat/two_sum.py");
        clearEditorText();

        // WHen
        WebElement presentation = findElement(wait, By.cssSelector("div[role='presentation']"));
        actions.contextClick(presentation).perform();
        RightClickContextMenu rightClickContextMenu = new RightClickContextMenu(driver, wait);
        TimeUtil.doDelayInSeconds(3);
        rightClickContextMenu.closeContextMenu(presentation);
        TimeUtil.doDelayInSeconds(1);
        ChatWindow chatWindow = rightClickContextMenu.triggerUnitTestGenerationFromContextMenu(presentation);
        chatWindow.stepInto();
        WebElement chatHistory = chatWindow.waitUntilChatFinish();

        // Then
        assertTrue(chatHistory.getText().contains(expectedMessagePart));
    }

}