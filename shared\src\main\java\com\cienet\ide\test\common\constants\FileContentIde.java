package com.cienet.ide.test.common.constants;

public class FileContentIde {
    // Java
    public static final String EFFICIENT_LOOP_JAVA = "public class InEfficientLoop {\n    public static void main(String[] args) {\n        int sum = 0;\n\n        // Efficient: Single loop, no extra array\n        for (int i = 1; i <= 100; i++) {\n            sum += i;\n        }\n\n        System.out.println(\"Sum: \" + sum);\n    }\n}";

    // Python
    public static final String FIND_ODD_NUMBERS_PYTHON = "def find_odd_numbers(arr, start, end):\n\n    odd_numbers = []\n    for i in range(start, end):\n        if arr[i] % 2 != 0:\n            odd_numbers.append(arr[i])\n    return odd_numbers";
    public static final String DEFINE_KERAS_MODEL = "# define the keras model\n\nmodel = Sequential()\n\nmodel.add(Dense(12, input_shape=(8,), activation='relu'))\n\nmodel.add(Dense(8, activation='relu'))\n\nmodel.add(Dense(1, activation='sigmoid'))";
    
    public static final String ADD_TWO_NUMBERS_PYTHON = "# write a function to add two numbers\ndef add_two_numbers(x, y):";
   
    public static final String SORT_ARRAY_PYTHON = "# Given an array of integers nums, sort the array in ascending order using quick sort and return it\ndef sortArray";
    public static final String SORT_ARRAY_JAVA = "// Given an array of integers nums, sort the array in ascending order using quick sort and return it\npublic int[] sortArray";
}
