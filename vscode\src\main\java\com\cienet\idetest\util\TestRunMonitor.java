package com.cienet.idetest.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.extension.AfterAllCallback;
import org.junit.jupiter.api.extension.AfterEachCallback;
import org.junit.jupiter.api.extension.AfterTestExecutionCallback;
import org.junit.jupiter.api.extension.BeforeAllCallback;
import org.junit.jupiter.api.extension.BeforeEachCallback;
import org.junit.jupiter.api.extension.BeforeTestExecutionCallback;
import org.junit.jupiter.api.extension.ExtensionContext;

@Slf4j
public class TestRunMonitor implements BeforeAllCallback, BeforeEachCallback, BeforeTestExecutionCallback,
        AfterTestExecutionCallback, AfterEachCallback, AfterAllCallback {

    @Override
    public void beforeAll(ExtensionContext context) throws Exception {
        // test instance is not present at this moment yet
        test(context, "beforeAll in: " + context.getDisplayName());
    }

    @Override
    public void beforeEach(ExtensionContext context) throws Exception {
        // test(context, "beforeEach"); driver=null
    }

    @Override
    public void beforeTestExecution(ExtensionContext context) throws Exception {
        // test(context, "beforeTestExecution");  driver=ChromeDriver: chrome on mac (49549042ab816b1f2fcf0890e40c81d9)
    }

    @Override
    public void afterTestExecution(ExtensionContext context) throws Exception {
        // test(context, "afterTestExecution"); driver=ChromeDriver: chrome on mac (null)
    }

    @Override
    public void afterEach(ExtensionContext context) throws Exception {
        // test(context, "afterEach"); driver=ChromeDriver: chrome on mac (null)
    }

    @Override
    public void afterAll(ExtensionContext context) throws Exception {
        // test instance is not present at this moment anymore
    }

    private void test(ExtensionContext context, String method) throws Exception {
        log.debug("trigger from " + method);

//        Object testInstance = context.getRequiredTestInstance();
//        Field field = testInstance.getClass().getDeclaredField("driver");
//        field.setAccessible(true);
//        WebDriver driver = (WebDriver) field.get(testInstance);
//        System.out.println("driver=" + driver);
    }
}
