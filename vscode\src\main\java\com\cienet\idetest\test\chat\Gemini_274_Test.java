package com.cienet.idetest.test.chat;

import com.cienet.idetest.test.ui.ChatWindow;
import com.cienet.idetest.test.ui.LeftManuBar;
import com.cienet.idetest.util.VSCodeUtil;
import com.cienet.idetest.util.TestExceptionHandler;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.WebDriverWait;

import java.time.Duration;

import static com.cienet.idetest.util.UiUtil.checkRestrictedModeAndAccept;
import static com.cienet.idetest.vo.TestConstant.WEBDRIVER_WAIT_TIME_SHORT;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(TestExceptionHandler.class)
public class Gemini_274_Test extends BaseChatTest {

    @Override
    @BeforeEach
    public void setup() {
        driver = VSCodeUtil.getChromeDriver();
        wait = new WebDriverWait(driver, Duration.ofSeconds(WEBDRIVER_WAIT_TIME_SHORT));
        checkRestrictedModeAndAccept(wait);
    }

    /**
     * Gemini-274 : Login Gemini from chat - Version2
     * To verify whether user can login from chat window
     *
     * Preconditions: Sign out from Gemini
     * Test compared with TesLink on: 3/10/2024
     */
    @Test
    public void test() {
        // Given
        // When
        LeftManuBar leftManuBar = new LeftManuBar(driver, wait);
        ChatWindow chatWindow = leftManuBar.openGeminiChat();
        chatWindow.stepInto();
        WebElement selectProjectButton = chatWindow.selectGoogleProjectButton();

        // Then
        assertTrue(selectProjectButton.isDisplayed());
    }

}
