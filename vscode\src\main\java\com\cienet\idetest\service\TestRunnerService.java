package com.cienet.idetest.service;

import com.cienet.idetest.logging.DebugLogFilter;
import com.cienet.idetest.test.BaseTest;
import com.cienet.idetest.util.VSCodeUtil;
import com.cienet.idetest.util.SessionUtil;
import com.cienet.ide.test.common.vo.TestSummary;
import com.cienet.ide.test.common.vo.VersionInfo;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.platform.engine.discovery.DiscoverySelectors;
import org.junit.platform.launcher.Launcher;
import org.junit.platform.launcher.LauncherDiscoveryRequest;
import org.junit.platform.launcher.core.LauncherDiscoveryRequestBuilder;
import org.junit.platform.launcher.core.LauncherFactory;
import org.junit.platform.launcher.listeners.SummaryGeneratingListener;
import org.junit.platform.launcher.listeners.TestExecutionSummary;
import org.reflections.Reflections;
import org.reflections.scanners.MethodAnnotationsScanner;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TestRunnerService {

    public static String TEST_PACKAGE = "com.cienet.idetest";

    private Reflections reflections = null;

    private Map<String, Method> testMethodMap = null;

    @PostConstruct
    public void init() {
        log.info("Initializing TestRunnerService...");
        System.setProperty("java.awt.headless", "false");
        reflections = new Reflections(TEST_PACKAGE, new MethodAnnotationsScanner());
        log.info("Reflections initialized with package: {}", TEST_PACKAGE);
        testMethodMap = reflections.getMethodsAnnotatedWith(org.junit.jupiter.api.Test.class).stream().collect(
                Collectors.toMap(method -> method.getDeclaringClass().getSimpleName(), method -> method));
    }

    public TestSummary runTestByTestName(String testName) {
        Set<Method> testMethods = reflections.getMethodsAnnotatedWith(org.junit.jupiter.api.Test.class);
        TestExecutionSummary summary = null;
        testName = testName + "_Test";
        long startTime = System.currentTimeMillis();
        log.info("runTestByTestName() enter...testName={}, resultOid={}, startTime={}",
                testName, SessionUtil.getResultOid(), startTime);
        Method method = testMethodMap.get(testName);
        log.debug("method: {}", method);

        if (method == null) {
            throw new RuntimeException("Test method not found! " + testName);
        }

        try {
            Class<?> testClass = method.getDeclaringClass();
            LauncherDiscoveryRequest request = LauncherDiscoveryRequestBuilder.request()
                    .selectors(DiscoverySelectors.selectMethod(testClass, method)).build();

            Launcher launcher = LauncherFactory.create();
            SummaryGeneratingListener listener = new SummaryGeneratingListener();
            launcher.registerTestExecutionListeners(listener);
            launcher.execute(request);

            summary = listener.getSummary();
        } catch (Throwable e) {
            log.error("Failed to run testcase: ", e);
            throw new RuntimeException(e);
        }

        return toTestSummary(summary, testName, startTime);
    }

    public List<String> getAvailableTestCases() {
        Set<Method> testMethods = reflections.getMethodsAnnotatedWith(org.junit.jupiter.api.Test.class);
        return testMethods.stream().map(method->method.getDeclaringClass().getSimpleName())
                .map(simpleName -> simpleName.substring(0, simpleName.length() - 5))
                .sorted().distinct().collect(Collectors.toList());
    }

    private TestSummary toTestSummary(TestExecutionSummary summary, String testName, long startTime) {
        String exceptionMessage = "";
        if (StringUtils.isEmpty(exceptionMessage) && summary.getTestsFailedCount() > 0) {
            exceptionMessage = summary.getFailures().get(0).getException().getMessage();
        }

        log.info("Test Execution Summary: " + "\n" +
                "======================" + "\n" +
                "Tests started: " + testName + "\n" +
                "Tests successful: " + summary.getTestsSucceededCount() + "\n" +
                "Tests failed: " + summary.getTestsFailedCount() + "\n" +
                "Tests aborted: " + summary.getTestsAbortedCount() + "\n" +
                "Tests skipped: " + summary.getTestsSkippedCount() + "\n" +
                "Execution time: " + (summary.getTimeFinished() - summary.getTimeStarted()) + "\n" +
                "======================");

        String agentLog = DebugLogFilter.getTestLogContent();

        TestSummary.TestResult testResult = null;

        if (summary.getTestsSucceededCount() == 1) {
            testResult = TestSummary.TestResult.Successful;
        } else if (summary.getTestsAbortedCount() == 1) {
            testResult = TestSummary.TestResult.Aborted;
        } else {
            testResult = TestSummary.TestResult.Failed;
        }

        TestSummary testSummary = new TestSummary();
        testSummary.setTestName(testName);
        VersionInfo versionInfo = BaseTest.getVersionInfo();

        if (versionInfo != null) {
            testSummary.setIdeVersion(versionInfo.ideVersion());
            testSummary.setPluginVersion(versionInfo.pluginVersion());
        } else {
            log.warn("No versionInfo found!");
        }

        testSummary.setTestResult(testResult);
        testSummary.setExecutionTime(summary.getTimeFinished() - summary.getTimeStarted());
        testSummary.setAgentLog(agentLog);
        testSummary.setExceptionMessage(exceptionMessage);

        if (testResult != TestSummary.TestResult.Successful) {
            testSummary.setScreenShot(VSCodeUtil.getScreenShotBase64(testName));
        }

        return testSummary;
    }

}
