package com.cienet.idetest.test.ui;

import com.cienet.idetest.util.CommonUtil;
import com.cienet.idetest.util.UiUtil;
import com.cienet.idetest.util.RobotUtil;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.support.ui.WebDriverWait;

import static java.lang.Thread.sleep;
import static com.cienet.idetest.util.UiUtil.findElement;

public class RightClickContextMenu {

    private final Actions actions;

    private final WebDriver driver;

    private final WebDriverWait wait;

    public RightClickContextMenu(WebDriver driver, WebDriverWait wait) {
        this.driver = driver;
        this.wait = wait;
        this.actions = new Actions(driver);
        // Wait for Gemini Code Assist to be ready
        UiUtil.findElement(wait, By.xpath("//div[@id='google.geminicodeassist']" +
                "//span[contains(@class, 'codicon-material-spark')]"));
    }

    public ChatWindow triggerCodeExplain(WebElement webElement) {
        actions.contextClick(webElement)
                .perform();
        return selectCodeExplanationFromContextMenu();
    }

    public ChatWindow selectCodeExplanationFromContextMenu() {
        ChatWindow chatWindow = new ChatWindow(driver, wait);
        try {
            sleep(3000);
            RobotUtil.clickChatInCtxMenu();
            sleep(3000);
            RobotUtil.clickEnterKey();
            sleep(6000);
            chatWindow.stepInto();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return chatWindow;
    }

    // If the response is only 1 line, finding the accept button might cause error.
    public WebElement triggerCodeGenFromContextMenu(WebDriverWait wait, WebElement webElement, boolean findAccept) {
        actions.contextClick(webElement).perform();
        try {
            RobotUtil.clickGenerateInCtxMenu();
            if (findAccept) {
                return findElement(wait,
                        By.xpath("//*[contains(@class,'inlineSuggestionsHints')]//*[contains(text(),'Accept')]"));
            } else {
                return null;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public ChatWindow triggerGenUnitTestFromContextMenu(WebDriverWait wait, WebElement webElement) {
        actions.contextClick(webElement).perform();
        try {
            RobotUtil.clickGenerateUnitTestInCtxMenu();
            return new ChatWindow(driver, wait);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    // public WebElement triggerCodeGenerationFromContextMenu(WebDriverWait wait, WebElement webElement) {
    //     fileToExecute("/src/main/resources/context_menu_generate_code.scpt", webElement);
    //     return findElement(wait, By.xpath("//*[contains(text(),'Accept')]"));
    // }

    public ChatWindow triggerUnitTestGenerationFromContextMenu(WebElement webElement) {
        fileToExecute("/src/main/resources/context_menu_generate_unit_test.scpt", webElement);
        return new ChatWindow(driver, wait);
    }

    public void closeContextMenu(WebElement webElement) {
        fileToExecute("/src/main/resources/context_menu_close.scpt", webElement);
    }

    private void fileToExecute(String pathToFile, WebElement webElement) {
        actions.contextClick(webElement)
                .perform();
        try {
            Runtime.getRuntime().exec("osascript " + CommonUtil.getCurrentDir() + pathToFile);
            sleep(6000);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
