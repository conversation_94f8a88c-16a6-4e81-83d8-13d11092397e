package org.cienet.ide_intellij_automation_test.test.codeGeneration;

import org.cienet.ide_intellij_automation_test.util.TimeUtil;
import org.cienet.ide_intellij_automation_test.util.CommonUtil;
import org.cienet.ide_intellij_automation_test.util.TestExceptionHandler;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import com.intellij.remoterobot.fixtures.EditorFixture;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertTrue;

import static com.intellij.remoterobot.search.locators.Locators.byXpath;
import java.time.Duration;

import com.intellij.remoterobot.utils.Keyboard;
import java.awt.event.KeyEvent;

import com.intellij.remoterobot.RemoteRobot;
import com.intellij.remoterobot.fixtures.CommonContainerFixture;
import com.intellij.remoterobot.fixtures.EditorFixture;
import com.intellij.remoterobot.fixtures.JPopupMenuFixture;
import com.intellij.remoterobot.fixtures.JTextFieldFixture;
import com.intellij.remoterobot.utils.Keyboard;

import org.cienet.ide_intellij_automation_test.util.IntellijUtil;
@ExtendWith(TestExceptionHandler.class)
public class Gemini_15_Test extends BaseGenerationTest {

    @Test
    void Gemini_15() throws IOException {
        closeAllCurrentTabs();
        openEmptyFileWithMultiplesFiles();
        String prompt = """
            def add(a, b : int) -> int:
            """;

        EditorFixture editor = CommonUtil.prepareEditor(remoteRobot);

        Keyboard keyboard = new Keyboard(remoteRobot);

        CommonUtil.enterTextByLines(keyboard, prompt);

        int initialTextLength = editor.getText().length();

        editor.rightClick();
        var menu = remoteRobot.find(
                JPopupMenuFixture.class,
                byXpath("//div[@class='HeavyWeightWindow']"),
                Duration.ofSeconds(10));
        menu.findText("Gemini").click();

        var generateMenu = remoteRobot.find(
                CommonContainerFixture.class,
                byXpath("//div[@text='Gemini']//div[@text='Generate Code']"),
                Duration.ofSeconds(10));
        generateMenu.click();

        waitAndRefocusEditor(remoteRobot, keyboard, editor);

        String generatedText = editor.getText();
        int generatedTextLength = generatedText.trim().length();

        assertTrue(
            generatedTextLength > initialTextLength,
            String.format("Code generation failed. Generated text:\n%s\n", generatedText)
        );

        TimeUtil.doDelay(5000);
        editor = CommonUtil.prepareEditor(remoteRobot);
    }

    private void waitAndRefocusEditor(RemoteRobot remoteRobot, Keyboard keyboard, EditorFixture editor) {
        IntellijUtil.waitShowCodeSuggestion(remoteRobot, 30);
        keyboard.key(KeyEvent.VK_TAB);
        editor.click();
    }
}
