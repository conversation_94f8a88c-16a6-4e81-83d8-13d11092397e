plugins {
    id 'java-library'
}

group = 'org.cienet'
version = '1.0.0'

java {
    sourceCompatibility = '21'
    targetCompatibility = '21'
}

repositories {
    mavenCentral()
}

dependencies {
    // Common utilities
    api 'org.apache.commons:commons-lang3:3.12.0'
    
    // Optional: For enhanced equality/hashcode operations
    api 'org.springframework.boot:spring-boot-starter:3.2.5'
    
    // Test dependencies
    testImplementation 'org.junit.jupiter:junit-jupiter:5.9.2'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'

    implementation 'ch.randelshofer:org.monte.media:17.2.1'
    implementation 'ch.randelshofer:org.monte.media.screenrecorder:17.2.1'

    compileOnly 'org.projectlombok:lombok:1.18.32'
    annotationProcessor 'org.projectlombok:lombok:1.18.32'
}

test {
    useJUnitPlatform()
}